# Technical Improvement Plan: PostgreSQL-Based Architecture

## Overview
This document outlines the technical improvement plan for Project Brief Buddy, focusing on building a robust backend with PostgreSQL (without Supabase) and refactoring the frontend for production readiness.

## Technology Stack

### Backend Stack
- **Runtime**: Node.js with Express/Fastify
- **Language**: TypeScript
- **Database**: PostgreSQL 15+
- **Cache**: Redis (sessions & caching)
- **ORM**: TypeORM/Prisma
- **Auth**: JWT with refresh tokens
- **File Upload**: Multer + S3/Local storage
- **Job Queue**: Bull with Redis
- **API Docs**: OpenAPI/Swagger

### Frontend Updates
- **State Management**: Zustand/Redux Toolkit
- **Data Fetching**: TanStack Query (React Query)
- **Form Validation**: React Hook Form + Zod
- **Testing**: Vitest + React Testing Library
- **E2E Testing**: Playwright

## Phase 1: Backend Infrastructure (Weeks 1-2)

### Project Structure
```
project-brief-buddy/
├── backend/
│   ├── src/
│   │   ├── config/
│   │   │   ├── database.ts
│   │   │   ├── redis.ts
│   │   │   └── environment.ts
│   │   ├── controllers/
│   │   │   ├── auth.controller.ts
│   │   │   ├── projects.controller.ts
│   │   │   ├── recordings.controller.ts
│   │   │   └── briefs.controller.ts
│   │   ├── services/
│   │   │   ├── auth.service.ts
│   │   │   ├── projects.service.ts
│   │   │   ├── transcription.service.ts
│   │   │   └── storage.service.ts
│   │   ├── repositories/
│   │   │   ├── base.repository.ts
│   │   │   ├── user.repository.ts
│   │   │   └── project.repository.ts
│   │   ├── models/
│   │   │   ├── user.model.ts
│   │   │   ├── project.model.ts
│   │   │   └── recording.model.ts
│   │   ├── middlewares/
│   │   │   ├── auth.middleware.ts
│   │   │   ├── validation.middleware.ts
│   │   │   ├── error.middleware.ts
│   │   │   └── rateLimit.middleware.ts
│   │   ├── validators/
│   │   │   ├── auth.validator.ts
│   │   │   └── project.validator.ts
│   │   ├── utils/
│   │   │   ├── logger.ts
│   │   │   ├── jwt.ts
│   │   │   └── bcrypt.ts
│   │   ├── types/
│   │   │   ├── express.d.ts
│   │   │   └── common.types.ts
│   │   ├── routes/
│   │   │   └── v1/
│   │   │       ├── index.ts
│   │   │       ├── auth.routes.ts
│   │   │       └── projects.routes.ts
│   │   └── app.ts
│   ├── migrations/
│   ├── seeds/
│   ├── tests/
│   └── package.json
├── frontend/
├── shared/
│   └── types/
└── docker-compose.yml
```

### Database Schema

```sql
-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users and Authentication
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(255),
    avatar_url VARCHAR(500),
    role VARCHAR(50) DEFAULT 'user' CHECK (role IN ('user', 'admin', 'viewer')),
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    last_login_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Organizations for multi-tenancy
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    subscription_tier VARCHAR(50) DEFAULT 'free',
    max_projects INTEGER DEFAULT 10,
    max_storage_gb INTEGER DEFAULT 5,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User-Organization relationship
CREATE TABLE organization_members (
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    role VARCHAR(50) DEFAULT 'member' CHECK (role IN ('owner', 'admin', 'member')),
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (organization_id, user_id)
);

-- Projects
CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    company VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'draft', 'completed', 'archived')),
    contact_email VARCHAR(255),
    contact_phone VARCHAR(50),
    start_date DATE,
    end_date DATE,
    organization_id UUID REFERENCES organizations(id),
    created_by UUID REFERENCES users(id),
    assigned_to UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(organization_id, slug)
);

-- Project Tags
CREATE TABLE project_tags (
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    tag VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (project_id, tag)
);

-- Recordings
CREATE TABLE recordings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    duration INTEGER, -- seconds
    file_path VARCHAR(500),
    file_size BIGINT,
    mime_type VARCHAR(100),
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    metadata JSONB, -- Store additional metadata
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP
);

-- Transcriptions
CREATE TABLE transcriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    recording_id UUID REFERENCES recordings(id) ON DELETE CASCADE,
    content TEXT,
    formatted_content JSONB, -- Store structured transcription with timestamps
    language VARCHAR(10) DEFAULT 'en',
    confidence DECIMAL(3,2),
    word_count INTEGER,
    duration INTEGER,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    error_message TEXT,
    service_used VARCHAR(50), -- 'whisper', 'google', 'aws'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP
);

-- Project Briefs
CREATE TABLE project_briefs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    title VARCHAR(255),
    content TEXT, -- Markdown content
    structured_data JSONB, -- Store structured requirements
    version INTEGER DEFAULT 1,
    is_current BOOLEAN DEFAULT true,
    is_approved BOOLEAN DEFAULT false,
    approved_by UUID REFERENCES users(id),
    approved_at TIMESTAMP,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Brief Comments
CREATE TABLE brief_comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    brief_id UUID REFERENCES project_briefs(id) ON DELETE CASCADE,
    parent_id UUID REFERENCES brief_comments(id),
    content TEXT NOT NULL,
    line_number INTEGER, -- For inline comments
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Project Checklists
CREATE TABLE project_checklists (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    item_key VARCHAR(50) NOT NULL,
    item_label VARCHAR(255) NOT NULL,
    is_completed BOOLEAN DEFAULT false,
    completed_at TIMESTAMP,
    completed_by UUID REFERENCES users(id),
    notes TEXT,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(project_id, item_key)
);

-- Audit Log
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    organization_id UUID REFERENCES organizations(id),
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    entity_id UUID NOT NULL,
    changes JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Sessions (for refresh tokens)
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    refresh_token_hash VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance
CREATE INDEX idx_projects_status ON projects(status);
CREATE INDEX idx_projects_organization ON projects(organization_id);
CREATE INDEX idx_projects_created_by ON projects(created_by);
CREATE INDEX idx_recordings_project ON recordings(project_id);
CREATE INDEX idx_recordings_status ON recordings(status);
CREATE INDEX idx_transcriptions_recording ON transcriptions(recording_id);
CREATE INDEX idx_briefs_project ON project_briefs(project_id);
CREATE INDEX idx_briefs_current ON project_briefs(project_id, is_current);
CREATE INDEX idx_project_tags_tag ON project_tags(tag);
CREATE INDEX idx_audit_logs_user ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_entity ON audit_logs(entity_type, entity_id);
CREATE INDEX idx_sessions_user ON user_sessions(user_id);
CREATE INDEX idx_sessions_expires ON user_sessions(expires_at);

-- Full-text search
CREATE INDEX idx_projects_search ON projects USING gin(to_tsvector('english', name || ' ' || COALESCE(description, '')));
CREATE INDEX idx_briefs_search ON project_briefs USING gin(to_tsvector('english', title || ' ' || content));
```

## Phase 2: API Implementation (Week 2)

### Authentication Flow
```typescript
// JWT Configuration
interface JWTConfig {
  accessTokenSecret: string;
  refreshTokenSecret: string;
  accessTokenExpiry: '15m';
  refreshTokenExpiry: '7d';
}

// Auth Endpoints
POST   /api/v1/auth/register
POST   /api/v1/auth/login
POST   /api/v1/auth/refresh
POST   /api/v1/auth/logout
POST   /api/v1/auth/forgot-password
POST   /api/v1/auth/reset-password
GET    /api/v1/auth/verify-email/:token
```

### Core API Endpoints
```typescript
// Projects
GET    /api/v1/projects?page=1&limit=20&status=active&search=term
POST   /api/v1/projects
GET    /api/v1/projects/:id
PUT    /api/v1/projects/:id
DELETE /api/v1/projects/:id
GET    /api/v1/projects/:id/export?format=pdf

// Recordings
POST   /api/v1/projects/:projectId/recordings/upload
GET    /api/v1/recordings/:id
GET    /api/v1/recordings/:id/stream
DELETE /api/v1/recordings/:id
POST   /api/v1/recordings/:id/transcribe

// Transcriptions
GET    /api/v1/transcriptions/:id
PUT    /api/v1/transcriptions/:id
POST   /api/v1/transcriptions/:id/regenerate

// Briefs
GET    /api/v1/projects/:projectId/briefs
POST   /api/v1/projects/:projectId/briefs
GET    /api/v1/briefs/:id
PUT    /api/v1/briefs/:id
POST   /api/v1/briefs/:id/approve
GET    /api/v1/briefs/:id/versions
POST   /api/v1/briefs/:id/comments

// Real-time
WS     /api/v1/ws (WebSocket for real-time updates)
```

## Phase 3: Frontend Refactoring (Week 3)

### State Management Setup
```typescript
// src/store/index.ts
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  login: (credentials: LoginDto) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
}

interface ProjectState {
  projects: Project[];
  currentProject: Project | null;
  filters: ProjectFilters;
  fetchProjects: () => Promise<void>;
  createProject: (data: CreateProjectDto) => Promise<void>;
  updateProject: (id: string, data: UpdateProjectDto) => Promise<void>;
}
```

### Service Layer
```typescript
// src/services/api.ts
import axios from 'axios';
import { useAuthStore } from '@/store/auth';

const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3001/api/v1',
  timeout: 30000,
});

// Request interceptor for auth
api.interceptors.request.use((config) => {
  const token = useAuthStore.getState().token;
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response interceptor for token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      await useAuthStore.getState().refreshToken();
      return api(error.config);
    }
    return Promise.reject(error);
  }
);

export default api;
```

### Custom Hooks
```typescript
// src/hooks/useProjects.ts
export const useProjects = (filters?: ProjectFilters) => {
  return useQuery({
    queryKey: ['projects', filters],
    queryFn: () => projectService.getAll(filters),
    staleTime: 5 * 60 * 1000,
    cacheTime: 10 * 60 * 1000,
  });
};

// src/hooks/useRecording.ts
export const useRecording = () => {
  const [isRecording, setIsRecording] = useState(false);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const mediaRecorder = useRef<MediaRecorder | null>(null);

  const startRecording = async () => {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    mediaRecorder.current = new MediaRecorder(stream);
    // ... implementation
  };

  return { isRecording, startRecording, stopRecording, audioBlob };
};
```

## Phase 4: File Handling & Storage (Week 4)

### File Upload Implementation
```typescript
// Backend file handling
import multer from 'multer';
import { S3Client } from '@aws-sdk/client-s3';

const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB
  },
  fileFilter: (req, file, cb) => {
    const allowedMimes = ['audio/mpeg', 'audio/wav', 'audio/webm'];
    if (allowedMimes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type'));
    }
  },
});

// Storage service
class StorageService {
  async uploadRecording(file: Express.Multer.File, projectId: string) {
    const key = `recordings/${projectId}/${Date.now()}-${file.originalname}`;
    // Upload to S3 or local storage
    return { url, key, size: file.size };
  }

  async streamFile(key: string) {
    // Return readable stream for audio playback
  }
}
```

## Phase 5: Testing Strategy (Week 5)

### Backend Testing
```typescript
// Unit test example
describe('ProjectService', () => {
  let projectService: ProjectService;
  let mockRepository: jest.Mocked<ProjectRepository>;

  beforeEach(() => {
    mockRepository = createMockRepository();
    projectService = new ProjectService(mockRepository);
  });

  it('should create project with proper validation', async () => {
    const projectData = {
      name: 'Test Project',
      company: 'Test Corp',
      description: 'Test description',
    };

    mockRepository.create.mockResolvedValue({ id: 'uuid', ...projectData });

    const result = await projectService.create(projectData, 'user-id');
    
    expect(result).toHaveProperty('id');
    expect(mockRepository.create).toHaveBeenCalledWith(
      expect.objectContaining(projectData)
    );
  });
});
```

### Frontend Testing
```typescript
// Component test
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ProjectDashboard } from '@/pages/ProjectDashboard';

describe('ProjectDashboard', () => {
  it('should display projects after loading', async () => {
    render(<ProjectDashboard />);
    
    expect(screen.getByText(/loading/i)).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.getByText('Test Project')).toBeInTheDocument();
    });
  });
});
```

### E2E Testing
```typescript
// Playwright test
import { test, expect } from '@playwright/test';

test('complete project creation flow', async ({ page }) => {
  await page.goto('/');
  
  // Login
  await page.fill('[name="email"]', '<EMAIL>');
  await page.fill('[name="password"]', 'password');
  await page.click('button[type="submit"]');
  
  // Create project
  await page.click('text=New Project');
  await page.fill('[name="name"]', 'E2E Test Project');
  await page.fill('[name="company"]', 'Test Company');
  await page.click('text=Create Project');
  
  // Verify creation
  await expect(page.locator('text=E2E Test Project')).toBeVisible();
});
```

## Phase 6: DevOps & Deployment (Week 6)

### Docker Configuration
```dockerfile
# Backend Dockerfile
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY --from=builder /app/dist ./dist
EXPOSE 3001
CMD ["node", "dist/app.js"]
```

### CI/CD Pipeline
```yaml
# .github/workflows/deploy.yml
name: Deploy
on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run tests
        run: |
          npm ci
          npm test
          npm run test:e2e

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to production
        run: |
          # Deploy scripts
```

## Implementation Timeline

### Week 1-2: Backend Foundation
- [ ] Set up Node.js project with TypeScript
- [ ] Configure PostgreSQL and create schema
- [ ] Implement authentication system
- [ ] Create base repositories and services
- [ ] Set up logging and error handling

### Week 3: API Development
- [ ] Implement all CRUD endpoints
- [ ] Add validation middleware
- [ ] Set up file upload handling
- [ ] Implement WebSocket for real-time
- [ ] Create API documentation

### Week 4: Frontend Integration
- [ ] Set up state management
- [ ] Create service layer
- [ ] Replace mock data with API calls
- [ ] Add error handling and loading states
- [ ] Implement authentication flow

### Week 5: Advanced Features
- [ ] Implement audio recording
- [ ] Add file streaming
- [ ] Create export functionality
- [ ] Add search and filtering
- [ ] Implement real-time updates

### Week 6: Testing & Deployment
- [ ] Write unit tests (80% coverage)
- [ ] Add integration tests
- [ ] Create E2E test suite
- [ ] Set up CI/CD pipeline
- [ ] Deploy to production

## Performance Targets
- API response time: < 200ms (p95)
- Frontend bundle size: < 200KB (gzipped)
- Time to Interactive: < 3s
- Database query time: < 50ms (p95)
- Test coverage: > 80%

## Security Checklist
- [ ] Input validation on all endpoints
- [ ] SQL injection prevention
- [ ] XSS protection
- [ ] CSRF tokens
- [ ] Rate limiting
- [ ] Secure headers (Helmet.js)
- [ ] File upload validation
- [ ] Audit logging
- [ ] Data encryption at rest
- [ ] HTTPS only