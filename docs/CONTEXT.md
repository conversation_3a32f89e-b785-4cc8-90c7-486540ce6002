# Project Brief Buddy - Context Document

## Project Status Summary
**Date**: 2025-07-10  
**Current State**: Early prototype with mock data  
**Tech Stack**: React 18, TypeScript, Vite, Tailwind CSS, shadcn/ui  
**Backend**: Not implemented (planned: Node.js + PostgreSQL)

## Quick Overview
Project Brief Buddy is a web application designed to help capture and manage client project requirements through audio recordings, automatic transcription, and AI-powered brief generation. Currently, it's a frontend-only prototype with all data mocked.

## Key Technical Decisions Made
1. **Database**: PostgreSQL (without Supabase) for full control
2. **Backend**: Node.js with Express/Fastify and TypeScript
3. **Authentication**: JWT with refresh tokens
4. **File Storage**: S3-compatible storage for audio files
5. **State Management**: Zustand or Redux Toolkit
6. **Testing**: Vitest + Playwright for comprehensive coverage

## Current Implementation Status

### ✅ Implemented
- Basic UI with shadcn/ui components
- Project dashboard with grid/list views
- Project detail page with tabs
- Create project dialog
- Basic markdown editor
- Responsive layout structure

### ❌ Not Implemented
- Backend API
- Database
- Authentication
- Audio recording (only UI mockup)
- File upload/storage
- Transcription service
- AI brief generation
- Real-time collaboration
- Export functionality (except basic markdown)

## Critical Issues to Address
1. **No Data Persistence**: Everything is lost on page refresh
2. **Security**: No authentication or authorization
3. **Type Safety**: Several TypeScript errors with `any` types
4. **Performance**: 404KB bundle size needs optimization
5. **Mock Data**: All data is hardcoded in components

## File Structure References
- Mock data location: `src/pages/ProjectDashboard.tsx:16-71`
- Recording interface: `src/components/meeting/RecordingInterface.tsx`
- Main routes: `src/App.tsx`
- TODO comments: Multiple files indicating Supabase integration plans

## Next Steps Priority
1. Set up backend with PostgreSQL
2. Implement authentication
3. Create API endpoints
4. Replace mock data with API calls
5. Implement actual audio recording
6. Add file upload functionality

## Related Documentation
- [Technical Analysis](./technical-analysis.md) - Detailed code analysis
- [Technical Improvements Plan](./technical-improvements-plan.md) - Implementation roadmap
- [Feature Gaps Analysis](./feature-gaps-analysis.md) - Missing features list

## Development Commands
```bash
npm run dev      # Start development server on port 8080
npm run build    # Build for production
npm run lint     # Run ESLint (currently has 5 errors, 7 warnings)
npm run preview  # Preview production build
```

## Known ESLint Issues
- 2 `any` type errors in RecordingInterface.tsx
- 2 empty interface errors
- 1 require() import error in tailwind.config.ts
- 7 fast refresh warnings in UI components

## Environment Variables Needed
```env
VITE_API_URL=http://localhost:3001/api/v1
DATABASE_URL=postgresql://user:password@localhost:5432/project_brief_buddy
JWT_SECRET=your-secret-key
REDIS_URL=redis://localhost:6379
S3_BUCKET=project-recordings
```

## Questions to Resolve
1. Preferred ORM: TypeORM vs Prisma?
2. File storage: S3 vs local storage for development?
3. Transcription service: OpenAI Whisper vs Google Speech-to-Text?
4. Deployment target: AWS, Vercel, or self-hosted?
5. Real-time: WebSockets vs Server-Sent Events?

---
This context document should be updated as the project evolves to maintain an accurate snapshot of the current state and decisions.