#!/bin/bash

# Demo user credentials for Project Brief Buddy
echo "Creating demo user for Project Brief Buddy..."

# Demo user details
EMAIL="<EMAIL>"
PASSWORD="demo123"
FULL_NAME="Demo User"

# API endpoint
API_URL="http://localhost:8080/api/auth/register"

# Create the user
curl -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -d "{
    \"email\": \"$EMAIL\",
    \"password\": \"$PASSWORD\",
    \"fullName\": \"$FULL_NAME\"
  }" \
  -w "\nHTTP Status: %{http_code}\n"

echo ""
echo "Demo Login Credentials:"
echo "======================="
echo "Email: $EMAIL"
echo "Password: $PASSWORD"
echo "Full Name: $FULL_NAME"
echo ""
echo "You can use these credentials to login to the application."
