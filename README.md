# Project Brief Buddy

A comprehensive tool for capturing and managing project requirements through audio recordings and automated brief generation.

## Features

### 🎯 Core Functionality
- **Project Management**: Create, organize, and track client projects
- **Audio Recording Upload**: Upload and manage meeting recordings
- **Brief Generation**: Create and edit project requirement documents
- **Real-time Collaboration**: WebSocket-powered live updates
- **User Authentication**: Secure JWT-based authentication system

### 🔧 Technical Features
- **Full-stack TypeScript**: Type-safe development
- **React Query**: Efficient data fetching and caching
- **Real-time Updates**: WebSocket integration for live collaboration
- **File Upload**: Secure audio file handling
- **Responsive Design**: Mobile-friendly interface
- **Error Handling**: Comprehensive error management

## Tech Stack

### Frontend
- **React 18** with TypeScript
- **Vite** for build tooling
- **Tailwind CSS** for styling
- **Shadcn/ui** component library
- **React Query** for state management
- **React Router** for navigation
- **Axios** for API calls
- **STOMP.js** for WebSocket connections

### Backend
- **Java 17** with Spring Boot 3.2
- **Spring Security** for authentication
- **Spring Data JPA** with PostgreSQL
- **JWT** for token-based auth
- **WebSocket** for real-time features
- **Maven** for dependency management
- **Swagger/OpenAPI** for documentation

### Database
- **PostgreSQL 15+** for data storage
- **JPA/Hibernate** for ORM
- **Database migrations** with Flyway

## Getting Started

### Prerequisites
- Node.js 18+
- Java 17+
- PostgreSQL 15+
- Maven 3.8+

### Backend Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd project-brief-buddy
   ```

2. **Set up PostgreSQL**
   ```bash
   # Create database
   createdb project_brief_buddy
   
   # Or using psql
   psql -U postgres -c "CREATE DATABASE project_brief_buddy;"
   ```

3. **Configure environment variables**
   ```bash
   # Set environment variables or update application.yml
   export DB_USERNAME=your_db_username
   export DB_PASSWORD=your_db_password
   export JWT_SECRET=your_jwt_secret_key
   ```

4. **Run the backend**
   ```bash
   cd backend
   mvn spring-boot:run
   ```

   The backend will start on `http://localhost:8080`

### Frontend Setup

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Run the frontend**
   ```bash
   npm run dev
   ```

   The frontend will start on `http://localhost:5173`

### Database Schema

The application will automatically create the database schema on startup. Key tables include:

- `users` - User accounts and authentication
- `projects` - Project information
- `recordings` - Audio recording metadata
- `briefs` - Project requirement documents
- `brief_sections` - Brief content sections
- `project_collaborators` - Project access control
- `refresh_tokens` - JWT refresh tokens

## API Documentation

Once the backend is running, you can access:
- **Swagger UI**: http://localhost:8080/swagger-ui.html
- **OpenAPI Docs**: http://localhost:8080/api-docs

## Usage

### 1. User Registration/Login
- Create an account or login with existing credentials
- JWT tokens are used for authentication
- Tokens are automatically refreshed

### 2. Project Management
- Create new projects with name and description
- View all projects in a grid or list layout
- Filter projects by status
- Search projects by name

### 3. Audio Recording Management
- Upload audio files (any audio format)
- Add descriptive titles to recordings
- View recording status and metadata
- Delete recordings when no longer needed

### 4. Brief Creation
- Create project requirement documents
- Use markdown editor for rich text formatting
- Save drafts and create final versions
- Track brief versions and status

### 5. Real-time Updates
- Automatic updates when other users make changes
- Live notifications for project activities
- WebSocket-powered collaboration features

## Development

### Running Tests
```bash
# Backend tests
cd backend
mvn test

# Frontend tests (if added)
npm test
```

### Building for Production
```bash
# Backend
cd backend
mvn clean package -DskipTests

# Frontend
npm run build
```

### Docker Support
```bash
# Build and run with Docker Compose
docker-compose up -d
```

## Architecture

### Frontend Architecture
```
src/
├── components/          # Reusable UI components
├── contexts/           # React contexts (Auth, etc.)
├── hooks/             # Custom React hooks
├── pages/             # Route components
├── services/          # API service classes
├── types/             # TypeScript type definitions
└── utils/             # Utility functions
```

### Backend Architecture
```
src/main/java/com/projectbriefbuddy/
├── config/            # Spring configuration
├── controller/        # REST controllers
├── dto/              # Data transfer objects
├── entity/           # JPA entities
├── exception/        # Custom exceptions
├── repository/       # Data repositories
├── security/         # Security configuration
└── service/          # Business logic services
```

## Security Features

- **JWT Authentication**: Secure token-based authentication
- **Password Hashing**: BCrypt password encryption
- **CORS Protection**: Configured for frontend domains
- **Input Validation**: Request validation and sanitization
- **File Upload Security**: Type and size validation
- **SQL Injection Prevention**: JPA/Hibernate protection

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support, please create an issue in the GitHub repository or contact the development team.

## Roadmap

### Phase 1 (Current)
- ✅ Basic project management
- ✅ Audio file upload
- ✅ Brief creation and editing
- ✅ User authentication
- ✅ Real-time updates

### Phase 2 (Future)
- 🔄 Audio transcription integration
- 🔄 AI-powered brief generation
- 🔄 Advanced collaboration features
- 🔄 Mobile app
- 🔄 Third-party integrations

### Phase 3 (Future)
- 🔄 Advanced analytics
- 🔄 Workflow automation
- 🔄 Multi-language support
- 🔄 Enterprise features