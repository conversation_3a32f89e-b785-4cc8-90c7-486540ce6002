// API Response Types
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  status: number;
}

export interface ErrorResponse {
  timestamp: string;
  status: number;
  error: string;
  message: string;
  validationErrors?: Record<string, string>;
}

// Authentication Types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  fullName: string;
  email: string;
  password: string;
}

export interface AuthResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
  user: User;
}

export interface RefreshTokenRequest {
  refresh_token: string;
}

// User Types
export interface User {
  id: string;
  email: string;
  fullName: string;
  role: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Project Types
export interface Project {
  id: string;
  name: string;
  description?: string;
  status: 'PLANNING' | 'IN_PROGRESS' | 'REVIEW' | 'FINALIZED';
  owner: User;
  recordingsCount: number;
  briefsCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface CreateProjectRequest {
  name: string;
  description?: string;
}

export interface UpdateProjectRequest {
  name?: string;
  description?: string;
  status?: 'PLANNING' | 'IN_PROGRESS' | 'REVIEW' | 'FINALIZED';
}

// Recording Types
export interface Recording {
  id: string;
  projectId: string;
  title: string;
  filePath?: string;
  fileSize?: number;
  duration?: number;
  mimeType?: string;
  transcription?: string;
  status: 'PENDING' | 'PROCESSING' | 'TRANSCRIBED' | 'FAILED';
  createdAt: string;
  updatedAt: string;
}

export interface UpdateRecordingRequest {
  title?: string;
  transcription?: string;
}

// Brief Types
export interface Brief {
  id: string;
  projectId: string;
  title: string;
  content?: string;
  version: number;
  status: 'DRAFT' | 'REVIEW' | 'APPROVED' | 'ARCHIVED';
  isFinal: boolean;
  sections?: BriefSection[];
  createdAt: string;
  updatedAt: string;
}

export interface BriefSection {
  id: string;
  title: string;
  content?: string;
  orderIndex: number;
  createdAt: string;
  updatedAt: string;
}

export interface CreateBriefRequest {
  title: string;
  content?: string;
  sections?: CreateBriefSectionRequest[];
}

export interface CreateBriefSectionRequest {
  title: string;
  content?: string;
  orderIndex?: number;
}

// Pagination Types
export interface PaginatedResponse<T> {
  content: T[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  first: boolean;
  last: boolean;
  numberOfElements: number;
  empty: boolean;
}

export interface PageRequest {
  page?: number;
  size?: number;
  sort?: string;
}

// WebSocket Types
export interface WebSocketMessage {
  type: string;
  projectId?: string;
  data: any;
  timestamp: number;
}