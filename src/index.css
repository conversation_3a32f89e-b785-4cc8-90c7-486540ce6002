@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 240 10% 98%;
    --foreground: 223 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 223 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 223 84% 4.9%;

    --primary: 221 83% 53%;
    --primary-foreground: 210 40% 98%;
    --primary-hover: 221 83% 48%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 223 47.4% 11.2%;

    --muted: 220 14% 96%;
    --muted-foreground: 220 8.9% 46.1%;

    --accent: 217 91% 95%;
    --accent-foreground: 221 83% 53%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 221 83% 53%;

    --success: 142 76% 36%;
    --success-foreground: 355 100% 97%;
    
    --warning: 38 92% 50%;
    --warning-foreground: 48 96% 89%;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-hover)));
    --gradient-card: linear-gradient(145deg, hsl(0 0% 100%), hsl(220 14% 99%));
    --gradient-muted: linear-gradient(180deg, hsl(var(--muted)), hsl(220 14% 94%));

    /* Shadows */
    --shadow-card: 0 2px 8px -2px hsl(var(--primary) / 0.1);
    --shadow-hover: 0 8px 25px -8px hsl(var(--primary) / 0.2);

    /* Animation */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 221 83% 53%;
    --primary-foreground: 210 40% 98%;
    --primary-hover: 221 83% 48%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 221 83% 53%;

    --success: 142 76% 36%;
    --success-foreground: 355 100% 97%;
    
    --warning: 38 92% 50%;
    --warning-foreground: 48 96% 89%;

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}