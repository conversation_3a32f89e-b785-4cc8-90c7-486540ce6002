import apiClient from './api';
import { 
  Brief, 
  CreateBriefRequest, 
  PaginatedResponse, 
  PageRequest 
} from '@/types/api';

export class BriefService {
  private getBasePath(projectId: string) {
    return `/api/projects/${projectId}/briefs`;
  }

  async getBriefs(projectId: string): Promise<Brief[]> {
    return apiClient.get<Brief[]>(this.getBasePath(projectId));
  }

  async getBriefsPaginated(
    projectId: string, 
    params?: PageRequest
  ): Promise<PaginatedResponse<Brief>> {
    const searchParams = new URLSearchParams();
    if (params?.page !== undefined) searchParams.append('page', params.page.toString());
    if (params?.size !== undefined) searchParams.append('size', params.size.toString());
    if (params?.sort) searchParams.append('sort', params.sort);

    const query = searchParams.toString();
    const url = query 
      ? `${this.getBasePath(projectId)}/paginated?${query}` 
      : `${this.getBasePath(projectId)}/paginated`;
    
    return apiClient.get<PaginatedResponse<Brief>>(url);
  }

  async getBrief(projectId: string, briefId: string): Promise<Brief> {
    return apiClient.get<Brief>(`${this.getBasePath(projectId)}/${briefId}`);
  }

  async createBrief(
    projectId: string, 
    data: CreateBriefRequest
  ): Promise<Brief> {
    return apiClient.post<Brief>(this.getBasePath(projectId), data);
  }

  async updateBrief(
    projectId: string, 
    briefId: string, 
    data: CreateBriefRequest
  ): Promise<Brief> {
    return apiClient.put<Brief>(
      `${this.getBasePath(projectId)}/${briefId}`, 
      data
    );
  }

  async finalizeBrief(projectId: string, briefId: string): Promise<Brief> {
    return apiClient.post<Brief>(
      `${this.getBasePath(projectId)}/${briefId}/finalize`
    );
  }

  async deleteBrief(projectId: string, briefId: string): Promise<void> {
    return apiClient.delete<void>(`${this.getBasePath(projectId)}/${briefId}`);
  }
}

export const briefService = new BriefService();