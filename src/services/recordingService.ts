import apiClient from './api';
import { 
  Recording, 
  UpdateRecordingRequest, 
  PaginatedResponse, 
  PageRequest 
} from '@/types/api';

export class RecordingService {
  private getBasePath(projectId: string) {
    return `/api/projects/${projectId}/recordings`;
  }

  async getRecordings(projectId: string): Promise<Recording[]> {
    return apiClient.get<Recording[]>(this.getBasePath(projectId));
  }

  async getRecordingsPaginated(
    projectId: string, 
    params?: PageRequest
  ): Promise<PaginatedResponse<Recording>> {
    const searchParams = new URLSearchParams();
    if (params?.page !== undefined) searchParams.append('page', params.page.toString());
    if (params?.size !== undefined) searchParams.append('size', params.size.toString());
    if (params?.sort) searchParams.append('sort', params.sort);

    const query = searchParams.toString();
    const url = query 
      ? `${this.getBasePath(projectId)}/paginated?${query}` 
      : `${this.getBasePath(projectId)}/paginated`;
    
    return apiClient.get<PaginatedResponse<Recording>>(url);
  }

  async getRecording(projectId: string, recordingId: string): Promise<Recording> {
    return apiClient.get<Recording>(`${this.getBasePath(projectId)}/${recordingId}`);
  }

  async createRecording(
    projectId: string, 
    file: File, 
    title: string
  ): Promise<Recording> {
    return apiClient.uploadFile<Recording>(
      this.getBasePath(projectId), 
      file, 
      { title }
    );
  }

  async updateRecording(
    projectId: string, 
    recordingId: string, 
    data: UpdateRecordingRequest
  ): Promise<Recording> {
    return apiClient.put<Recording>(
      `${this.getBasePath(projectId)}/${recordingId}`, 
      data
    );
  }

  async deleteRecording(projectId: string, recordingId: string): Promise<void> {
    return apiClient.delete<void>(`${this.getBasePath(projectId)}/${recordingId}`);
  }

  async downloadRecording(projectId: string, recordingId: string): Promise<Blob> {
    const response = await fetch(
      `${apiClient['client'].defaults.baseURL}${this.getBasePath(projectId)}/${recordingId}/download`,
      {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
      }
    );

    if (!response.ok) {
      throw new Error('Failed to download recording');
    }

    return response.blob();
  }

  getRecordingDownloadUrl(projectId: string, recordingId: string): string {
    return `${apiClient['client'].defaults.baseURL}${this.getBasePath(projectId)}/${recordingId}/download`;
  }
}

export const recordingService = new RecordingService();