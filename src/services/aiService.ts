import { apiClient } from './apiClient';

export interface AiConversationRequest {
  projectId: number;
  type: 'REQUIREMENTS_GATHERING' | 'REQUIREMENTS_REFINEMENT' | 'GAP_ANALYSIS' | 'STAKEHOLDER_INTERVIEW' | 'TECHNICAL_CLARIFICATION' | 'VALIDATION_REVIEW';
  initialContext?: string;
}

export interface AiConversation {
  id: number;
  title: string;
  type: string;
  status: 'ACTIVE' | 'PAUSED' | 'COMPLETED' | 'ARCHIVED';
  projectId: number;
  totalMessages: number;
  completionPercentage: number;
  createdAt: string;
  updatedAt: string;
}

export interface AiMessage {
  id: number;
  role: 'USER' | 'ASSISTANT' | 'SYSTEM';
  content: string;
  messageOrder: number;
  processingTimeMs?: number;
  createdAt: string;
}

export const aiService = {
  // Requirements Extraction
  extractRequirements: async (text: string): Promise<string> => {
    const response = await apiClient.post('/ai/extract-requirements', text, {
      headers: { 'Content-Type': 'text/plain' }
    });
    return response.data.data;
  },

  refineRequirements: async (currentRequirements: string): Promise<string> => {
    const response = await apiClient.post('/ai/refine-requirements', currentRequirements, {
      headers: { 'Content-Type': 'text/plain' }
    });
    return response.data.data;
  },

  generateQuestions: async (partialRequirements: string): Promise<string> => {
    const response = await apiClient.post('/ai/generate-questions', partialRequirements, {
      headers: { 'Content-Type': 'text/plain' }
    });
    return response.data.data;
  },

  // Audio Processing
  transcribeAudio: async (file: File): Promise<string> => {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await apiClient.post('/ai/transcribe-audio', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
    return response.data.data;
  },

  analyzeMeeting: async (transcription: string): Promise<string> => {
    const response = await apiClient.post('/ai/analyze-meeting', transcription, {
      headers: { 'Content-Type': 'text/plain' }
    });
    return response.data.data;
  },

  // AI Conversations
  startConversation: async (request: AiConversationRequest): Promise<AiConversation> => {
    const response = await apiClient.post('/ai/conversations', request);
    return response.data.data;
  },

  sendMessage: async (conversationId: number, message: string): Promise<string> => {
    const response = await apiClient.post(`/ai/conversations/${conversationId}/messages`, message, {
      headers: { 'Content-Type': 'text/plain' }
    });
    return response.data.data;
  },

  getConversationMessages: async (conversationId: number): Promise<AiMessage[]> => {
    const response = await apiClient.get(`/ai/conversations/${conversationId}/messages`);
    return response.data.data;
  },

  pauseConversation: async (conversationId: number): Promise<void> => {
    await apiClient.put(`/ai/conversations/${conversationId}/pause`);
  },

  resumeConversation: async (conversationId: number): Promise<void> => {
    await apiClient.put(`/ai/conversations/${conversationId}/resume`);
  },

  completeConversation: async (conversationId: number): Promise<void> => {
    await apiClient.put(`/ai/conversations/${conversationId}/complete`);
  },

  getProjectConversations: async (projectId: number): Promise<AiConversation[]> => {
    const response = await apiClient.get(`/ai/projects/${projectId}/conversations`);
    return response.data.data;
  },

  // Template Analysis
  analyzeTemplateGaps: async (currentRequirements: string): Promise<string> => {
    const response = await apiClient.post('/ai/analyze-template-gaps', currentRequirements, {
      headers: { 'Content-Type': 'text/plain' }
    });
    return response.data.data;
  },

  suggestProjectType: async (projectDescription: string): Promise<string> => {
    const response = await apiClient.post('/ai/suggest-project-type', projectDescription, {
      headers: { 'Content-Type': 'text/plain' }
    });
    return response.data.data;
  },

  generateChecklist: async (projectType: string, projectContext: string): Promise<string> => {
    const response = await apiClient.post(`/ai/generate-checklist?projectType=${encodeURIComponent(projectType)}`, projectContext, {
      headers: { 'Content-Type': 'text/plain' }
    });
    return response.data.data;
  },

  // Gap Analysis
  validateRequirements: async (projectId: number): Promise<string> => {
    const response = await apiClient.post(`/ai/projects/${projectId}/validate-requirements`);
    return response.data.data;
  },

  performGapAnalysis: async (projectId: number, projectType: string, industryStandards?: string): Promise<string> => {
    const params = new URLSearchParams({ projectType });
    if (industryStandards) {
      params.append('industryStandards', industryStandards);
    }
    
    const response = await apiClient.post(`/ai/projects/${projectId}/gap-analysis?${params}`);
    return response.data.data;
  },

  getQualityMetrics: async (projectId: number): Promise<string> => {
    const response = await apiClient.get(`/ai/projects/${projectId}/quality-metrics`);
    return response.data.data;
  }
};