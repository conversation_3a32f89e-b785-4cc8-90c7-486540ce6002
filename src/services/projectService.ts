import apiClient from './api';
import { 
  Project, 
  CreateProjectRequest, 
  UpdateProjectRequest, 
  PaginatedResponse, 
  PageRequest 
} from '@/types/api';

export class ProjectService {
  private basePath = '/api/projects';

  async getProjects(params?: PageRequest): Promise<PaginatedResponse<Project>> {
    const searchParams = new URLSearchParams();
    if (params?.page !== undefined) searchParams.append('page', params.page.toString());
    if (params?.size !== undefined) searchParams.append('size', params.size.toString());
    if (params?.sort) searchParams.append('sort', params.sort);

    const query = searchParams.toString();
    const url = query ? `${this.basePath}?${query}` : this.basePath;
    
    return apiClient.get<PaginatedResponse<Project>>(url);
  }

  async getProject(id: string): Promise<Project> {
    return apiClient.get<Project>(`${this.basePath}/${id}`);
  }

  async createProject(data: CreateProjectRequest): Promise<Project> {
    return apiClient.post<Project>(this.basePath, data);
  }

  async updateProject(id: string, data: UpdateProjectRequest): Promise<Project> {
    return apiClient.put<Project>(`${this.basePath}/${id}`, data);
  }

  async deleteProject(id: string): Promise<void> {
    return apiClient.delete<void>(`${this.basePath}/${id}`);
  }
}

export const projectService = new ProjectService();