import { Client } from '@stomp/stompjs';
import { WebSocketMessage } from '@/types/api';

class WebSocketService {
  private client: Client | null = null;
  private isConnected = false;
  private subscriptions: Map<string, () => void> = new Map();
  private messageHandlers: Map<string, (message: WebSocketMessage) => void> = new Map();

  constructor() {
    this.initializeClient();
  }

  private initializeClient() {
    const wsUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:8080/ws';
    
    this.client = new Client({
      brokerURL: wsUrl,
      debug: (str) => {
        console.log('WebSocket Debug:', str);
      },
      reconnectDelay: 5000,
      heartbeatIncoming: 4000,
      heartbeatOutgoing: 4000,
      onConnect: () => {
        console.log('WebSocket connected');
        this.isConnected = true;
        this.resubscribeAll();
      },
      onStompError: (frame) => {
        console.error('WebSocket error:', frame);
      },
      onWebSocketError: (error) => {
        console.error('WebSocket connection error:', error);
      },
      onDisconnect: () => {
        console.log('WebSocket disconnected');
        this.isConnected = false;
      },
    });
  }

  connect() {
    if (this.client && !this.isConnected) {
      try {
        this.client.activate();
      } catch (error) {
        console.error('Failed to connect WebSocket:', error);
      }
    }
  }

  disconnect() {
    if (this.client && this.isConnected) {
      this.client.deactivate();
      this.subscriptions.clear();
      this.messageHandlers.clear();
    }
  }

  private resubscribeAll() {
    // Re-establish all subscriptions after reconnection
    const handlers = Array.from(this.messageHandlers.entries());
    this.subscriptions.clear();
    this.messageHandlers.clear();
    
    handlers.forEach(([topic, handler]) => {
      this.subscribe(topic, handler);
    });
  }

  subscribe(topic: string, handler: (message: WebSocketMessage) => void) {
    if (!this.client) return;

    // Store the handler for reconnection
    this.messageHandlers.set(topic, handler);

    if (this.isConnected) {
      const subscription = this.client.subscribe(topic, (message) => {
        try {
          const parsedMessage: WebSocketMessage = JSON.parse(message.body);
          handler(parsedMessage);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      });

      this.subscriptions.set(topic, () => subscription.unsubscribe());
    }
  }

  unsubscribe(topic: string) {
    const unsubscribeFunc = this.subscriptions.get(topic);
    if (unsubscribeFunc) {
      unsubscribeFunc();
      this.subscriptions.delete(topic);
    }
    this.messageHandlers.delete(topic);
  }

  // Subscribe to project-specific updates
  subscribeToProject(projectId: string, handler: (message: WebSocketMessage) => void) {
    this.subscribe(`/topic/project/${projectId}`, handler);
  }

  // Subscribe to recording updates for a project
  subscribeToRecordings(projectId: string, handler: (message: WebSocketMessage) => void) {
    this.subscribe(`/topic/project/${projectId}/recordings`, handler);
  }

  // Subscribe to brief updates for a project
  subscribeToBriefs(projectId: string, handler: (message: WebSocketMessage) => void) {
    this.subscribe(`/topic/project/${projectId}/briefs`, handler);
  }

  // Subscribe to user-specific notifications
  subscribeToNotifications(userId: string, handler: (message: WebSocketMessage) => void) {
    this.subscribe(`/user/${userId}/queue/notifications`, handler);
  }

  // Unsubscribe from all project-related topics
  unsubscribeFromProject(projectId: string) {
    this.unsubscribe(`/topic/project/${projectId}`);
    this.unsubscribe(`/topic/project/${projectId}/recordings`);
    this.unsubscribe(`/topic/project/${projectId}/briefs`);
  }

  isConnectedToServer(): boolean {
    return this.isConnected;
  }
}

export const websocketService = new WebSocketService();
export default websocketService;