import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { ArrowLeft, Building2, Calendar, Tag, Loader2 } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { RecordingInterface } from "@/components/meeting/RecordingInterface";
import { MarkdownEditor } from "@/components/editor/MarkdownEditor";
import { ProjectChecklist } from "@/components/checklist/ProjectChecklist";
import { AiAssistant } from "@/components/ai/AiAssistant";
import { useProject } from "@/hooks/useProjects";
import { useBriefs, useCreateBrief } from "@/hooks/useBriefs";
import { useRecordings } from "@/hooks/useRecordings";
import { useProjectWebSocket } from "@/hooks/useWebSocket";

export default function ProjectDetail() {
  const { id } = useParams<{ id: string }>();
  const [briefContent, setBriefContent] = useState("");
  
  const { data: project, isLoading: projectLoading, error: projectError } = useProject(id!);
  const { data: briefs = [], isLoading: briefsLoading } = useBriefs(id!);
  const { data: recordings = [] } = useRecordings(id!);
  const createBrief = useCreateBrief();
  
  // Set up WebSocket connection for real-time updates
  useProjectWebSocket(id!);
  
  // Get the latest brief or create a new one
  const latestBrief = briefs.length > 0 ? briefs[0] : null;
  
  // Generate dynamic checklist based on real data
  const generateChecklist = () => {
    if (!project) return [];
    
    return [
      {
        id: "recording",
        title: "Meeting Recording",
        description: "Upload or record the client meeting audio",
        completed: recordings.length > 0,
        required: true,
      },
      {
        id: "transcription",
        title: "Transcription Complete",
        description: "Generate or review meeting transcription",
        completed: recordings.some(r => r.transcription),
        required: true,
        canOverride: true,
      },
      {
        id: "brief",
        title: "Project Brief Written",
        description: "Complete the detailed project requirements document",
        completed: briefs.length > 0,
        required: true,
      },
      {
        id: "workflow",
        title: "Workflow Attached",
        description: "Include workflow diagrams or process documentation",
        completed: false,
        required: false,
      },
      {
        id: "mockups",
        title: "UI Mockups Included",
        description: "Attach design mockups or wireframes",
        completed: false,
        required: true,
      },
      {
        id: "externals",
        title: "External Systems Documented",
        description: "Document all third-party integrations and APIs",
        completed: false,
        required: false,
      },
    ];
  };
  
  const checklist = generateChecklist();

  const handleChecklistToggle = (itemId: string, completed: boolean) => {
    // For now, this is read-only since it's based on real data
    // In a full implementation, you might want to allow manual overrides
    console.log(`Checklist item ${itemId} toggled:`, completed);
  };

  const handleFinalizePRD = () => {
    // TODO: Implement finalization logic
    console.log("Finalizing PRD...", { briefContent, checklist });
  };

  const handleSaveBrief = async (content: string) => {
    setBriefContent(content);
    
    if (!id || !content.trim()) return;
    
    try {
      if (latestBrief) {
        // Update existing brief (would need update hook)
        console.log("Updating existing brief...", content);
      } else {
        // Create new brief
        await createBrief.mutateAsync({
          projectId: id,
          data: {
            title: `${project?.name} - Requirements`,
            content: content,
          },
        });
      }
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  if (projectLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-muted-foreground">Loading project...</p>
          </div>
        </div>
      </div>
    );
  }

  if (projectError || !project) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Project not found</h1>
          <p className="text-muted-foreground mb-4">
            The project you're looking for doesn't exist or you don't have access to it.
          </p>
          <Button asChild>
            <Link to="/">Return to Projects</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col gap-4">
          <Button variant="ghost" size="sm" asChild className="self-start">
            <Link to="/" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back to Projects
            </Link>
          </Button>
          
          <div className="flex flex-col lg:flex-row gap-6 items-start justify-between">
            <div className="space-y-4">
              <div>
                <h1 className="text-3xl font-bold mb-2">{project.name}</h1>
                <div className="flex items-center gap-2 text-muted-foreground mb-2">
                  <Building2 className="h-4 w-4" />
                  {project.owner.fullName}
                </div>
                <p className="text-muted-foreground max-w-2xl">
                  {project.description || "No description provided"}
                </p>
              </div>
              
              <div className="flex flex-wrap gap-2">
                <Badge variant="outline">
                  <Tag className="h-3 w-3 mr-1" />
                  {project.status}
                </Badge>
                {project.recordingsCount > 0 && (
                  <Badge variant="outline">
                    {project.recordingsCount} Recording{project.recordingsCount !== 1 ? 's' : ''}
                  </Badge>
                )}
                {project.briefsCount > 0 && (
                  <Badge variant="outline">
                    {project.briefsCount} Brief{project.briefsCount !== 1 ? 's' : ''}
                  </Badge>
                )}
              </div>
            </div>

            <Card className="w-full lg:w-80">
              <CardHeader>
                <CardTitle className="text-lg">Project Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 text-sm">
                <div className="flex items-center gap-2">
                  <Building2 className="h-4 w-4 text-muted-foreground" />
                  <span>Owner: {project.owner.fullName}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span>Created {new Date(project.createdAt).toLocaleDateString()}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span>Updated {new Date(project.updatedAt).toLocaleDateString()}</span>
                </div>
                <div className="pt-2">
                  <Badge className={
                    project.status === "IN_PROGRESS" ? "bg-primary" :
                    project.status === "FINALIZED" ? "bg-green-500" : 
                    project.status === "REVIEW" ? "bg-yellow-500" : "bg-muted"
                  }>
                    {project.status}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3 lg:grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="recording">Recording</TabsTrigger>
            <TabsTrigger value="brief">Brief</TabsTrigger>
            <TabsTrigger value="ai-assistant">AI Assistant</TabsTrigger>
            <TabsTrigger value="checklist">Checklist</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-6">
                <ProjectChecklist
                  checklist={checklist}
                  onToggle={handleChecklistToggle}
                  onFinalize={handleFinalizePRD}
                />
              </div>
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Quick Actions</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <Button variant="outline" className="w-full justify-start">
                      Upload Workflow Diagrams
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      Upload UI Mockups
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      Document External APIs
                    </Button>
                    <Button variant="gradient" className="w-full">
                      Generate Transcription (AI)
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="recording">
            <RecordingInterface projectId={project.id} />
          </TabsContent>

          <TabsContent value="brief">
            <MarkdownEditor
              initialContent={latestBrief?.content || briefContent}
              onSave={handleSaveBrief}
              title="Project Requirements Document"
            />
          </TabsContent>

          <TabsContent value="ai-assistant">
            <AiAssistant 
              projectId={parseInt(id!)}
              projectName={project.name}
              projectDescription={project.description}
            />
          </TabsContent>

          <TabsContent value="checklist">
            <ProjectChecklist
              checklist={checklist}
              onToggle={handleChecklistToggle}
              onFinalize={handleFinalizePRD}
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}