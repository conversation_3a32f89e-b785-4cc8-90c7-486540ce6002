import { useState } from "react";
import { Search, Filter, Grid3X3, List, Loader2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { CreateProjectDialog } from "@/components/projects/CreateProjectDialog";
import { ProjectCard } from "@/components/projects/ProjectCard";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useProjects } from "@/hooks/useProjects";
import { Project } from "@/types/api";

// Helper function to map API project status to UI status
const mapProjectStatus = (status: string) => {
  switch (status) {
    case 'PLANNING':
      return 'draft';
    case 'IN_PROGRESS':
      return 'active';
    case 'REVIEW':
      return 'active';
    case 'FINALIZED':
      return 'completed';
    default:
      return 'draft';
  }
};

// Helper function to calculate progress based on recordings and briefs
const calculateProgress = (project: Project) => {
  if (project.status === 'FINALIZED') return 100;
  if (project.status === 'REVIEW') return 80;
  if (project.status === 'IN_PROGRESS') return 50;
  return 25;
};

// Helper function to format date
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
  
  if (diffInDays === 0) return 'Today';
  if (diffInDays === 1) return '1 day ago';
  if (diffInDays < 7) return `${diffInDays} days ago`;
  if (diffInDays < 14) return '1 week ago';
  if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;
  return `${Math.floor(diffInDays / 30)} months ago`;
};

// Helper function to transform API project to UI project
const transformProject = (project: Project) => ({
  id: project.id,
  name: project.name,
  company: project.owner.fullName,
  description: project.description || 'No description provided',
  status: mapProjectStatus(project.status),
  progress: calculateProgress(project),
  lastUpdated: formatDate(project.updatedAt),
  tags: [], // Will be populated from project metadata in future
  checklist: {
    recording: project.recordingsCount > 0,
    transcription: project.recordingsCount > 0,
    brief: project.briefsCount > 0,
    workflow: false,
    mockups: false,
    externals: false,
  },
});

export default function ProjectDashboard() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

  console.log('ProjectDashboard: Component rendering');

  const { data: projectsResponse, isLoading, error } = useProjects({ size: 50 });

  console.log('ProjectDashboard:', { projectsResponse, isLoading, error });

  const projects = projectsResponse?.content || [];
  const transformedProjects = projects.map(transformProject);

  const filteredProjects = transformedProjects.filter((project) => {
    const matchesSearch = project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.company.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || project.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-muted-foreground">Loading projects...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center py-12">
          <div className="h-24 w-24 rounded-full bg-destructive/10 mx-auto mb-4 flex items-center justify-center">
            <Search className="h-8 w-8 text-destructive" />
          </div>
          <h3 className="text-lg font-medium mb-2">Failed to load projects</h3>
          <p className="text-muted-foreground mb-4">
            There was an error loading your projects. Please try again.
          </p>
          <Button onClick={() => window.location.reload()}>
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Projects</h1>
            <p className="text-muted-foreground mt-1">
              Manage your client project requirements and documentation
            </p>
          </div>
          <CreateProjectDialog />
        </div>

        {/* Filters and Search */}
        <div className="flex flex-col sm:flex-row gap-4 items-stretch sm:items-center justify-between">
          <div className="flex flex-col sm:flex-row gap-3 flex-1">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search projects..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex gap-2">
            <Button
              variant={viewMode === "grid" ? "secondary" : "ghost"}
              size="sm"
              onClick={() => setViewMode("grid")}
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === "list" ? "secondary" : "ghost"}
              size="sm"
              onClick={() => setViewMode("list")}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Projects Grid */}
        {filteredProjects.length > 0 ? (
          <div className={
            viewMode === "grid" 
              ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
              : "space-y-4"
          }>
            {filteredProjects.map((project) => (
              <ProjectCard key={project.id} project={project} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="h-24 w-24 rounded-full bg-muted mx-auto mb-4 flex items-center justify-center">
              <Search className="h-8 w-8 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-medium mb-2">No projects found</h3>
            <p className="text-muted-foreground mb-4">
              {searchTerm || statusFilter !== "all" 
                ? "Try adjusting your search or filters"
                : "Create your first project to get started"
              }
            </p>
            {!searchTerm && statusFilter === "all" && <CreateProjectDialog />}
          </div>
        )}
      </div>
    </div>
  );
}