import { useState, useRef } from "react";
import { Upload, Play, Trash2, Loader2, File<PERSON>udio, Sparkles } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { useCreateRecording, useRecordings, useDeleteRecording } from "@/hooks/useRecordings";
import { useTranscribeAudio, useAnalyzeMeeting } from "@/hooks/useAi";
import { Recording } from "@/types/api";

interface RecordingInterfaceProps {
  projectId: string;
}

export function RecordingInterface({ projectId }: RecordingInterfaceProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [recordingTitle, setRecordingTitle] = useState("");
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const { data: recordings = [], isLoading } = useRecordings(projectId);
  const createRecording = useCreateRecording();
  const deleteRecording = useDeleteRecording();
  const transcribeAudio = useTranscribeAudio();
  const analyzeMeeting = useAnalyzeMeeting();

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('audio/')) {
        toast({
          title: "Invalid file type",
          description: "Please select an audio file",
          variant: "destructive",
        });
        return;
      }
      
      // Validate file size (max 100MB)
      if (file.size > 100 * 1024 * 1024) {
        toast({
          title: "File too large",
          description: "Please select a file smaller than 100MB",
          variant: "destructive",
        });
        return;
      }
      
      setSelectedFile(file);
      setRecordingTitle(file.name.replace(/\.[^/.]+$/, "")); // Remove file extension
    }
  };

  const handleUpload = async () => {
    if (!selectedFile || !recordingTitle.trim()) {
      toast({
        title: "Missing information",
        description: "Please select a file and enter a title",
        variant: "destructive",
      });
      return;
    }

    try {
      await createRecording.mutateAsync({
        projectId,
        file: selectedFile,
        title: recordingTitle.trim(),
      });
      
      // Reset form
      setSelectedFile(null);
      setRecordingTitle("");
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  const handleDeleteRecording = async (recordingId: string) => {
    try {
      await deleteRecording.mutateAsync({ projectId, recordingId });
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  const handleTranscribeRecording = async (file: File) => {
    try {
      const transcription = await transcribeAudio.mutateAsync(file);
      const analysis = await analyzeMeeting.mutateAsync(transcription);
      
      toast({
        title: "Transcription completed",
        description: "Audio has been transcribed and analyzed successfully",
      });
      
      // You could store this transcription/analysis or use it to populate requirements
      console.log('Transcription:', transcription);
      console.log('Analysis:', analysis);
    } catch (error) {
      // Error handling is done in the hooks
    }
  };

  const formatDuration = (seconds: number) => {
    if (!seconds) return "Unknown duration";
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const formatFileSize = (bytes: number) => {
    if (!bytes) return "Unknown size";
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(1)} MB`;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin mr-3" />
            <span>Loading recordings...</span>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Upload Interface */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Upload Recording
          </CardTitle>
          <CardDescription>
            Upload audio files from your client meetings to process and transcribe
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="file">Select Audio File</Label>
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                  className="flex-1"
                >
                  <Upload className="h-4 w-4 mr-2" />
                  {selectedFile ? selectedFile.name : "Choose File"}
                </Button>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="audio/*"
                  onChange={handleFileSelect}
                  className="hidden"
                />
              </div>
              {selectedFile && (
                <p className="text-sm text-muted-foreground">
                  File size: {formatFileSize(selectedFile.size)}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="title">Recording Title</Label>
              <Input
                id="title"
                value={recordingTitle}
                onChange={(e) => setRecordingTitle(e.target.value)}
                placeholder="Enter a descriptive title for this recording"
                disabled={createRecording.isPending}
              />
            </div>

            <Button 
              onClick={handleUpload} 
              disabled={!selectedFile || !recordingTitle.trim() || createRecording.isPending}
              className="w-full"
            >
              {createRecording.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Uploading...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Recording
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Recordings List */}
      {recordings.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Uploaded Recordings</CardTitle>
            <CardDescription>
              Manage your meeting recordings and their transcriptions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recordings.map((recording) => (
                <div key={recording.id} className="flex items-center justify-between p-4 border border-border rounded-lg bg-muted/30">
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 bg-primary/10 rounded-lg flex items-center justify-center">
                      <FileAudio className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <p className="font-medium">{recording.title}</p>
                      <p className="text-sm text-muted-foreground">
                        {recording.fileSize && formatFileSize(recording.fileSize)} • {formatDate(recording.createdAt)}
                        {recording.duration && ` • ${formatDuration(recording.duration)}`}
                      </p>
                      {recording.status && (
                        <p className="text-xs text-muted-foreground mt-1">
                          Status: {recording.status}
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {recording.transcription && (
                      <Button variant="ghost" size="sm" title="View Transcription">
                        <Play className="h-4 w-4" />
                      </Button>
                    )}
                    {!recording.transcription && recording.filePath && (
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => {
                          // Create a mock file object for transcription
                          // In a real implementation, you'd fetch the actual file
                          const mockFile = new File([], recording.title, { type: 'audio/wav' });
                          handleTranscribeRecording(mockFile);
                        }}
                        disabled={transcribeAudio.isPending || analyzeMeeting.isPending}
                        title="Transcribe with AI"
                      >
                        {(transcribeAudio.isPending || analyzeMeeting.isPending) ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Sparkles className="h-4 w-4" />
                        )}
                      </Button>
                    )}
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => handleDeleteRecording(recording.id)}
                      disabled={deleteRecording.isPending}
                      title="Delete Recording"
                    >
                      {deleteRecording.isPending ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Trash2 className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}