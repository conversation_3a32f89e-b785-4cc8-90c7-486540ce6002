import { CalendarD<PERSON>, <PERSON>, <PERSON>Text, CheckCircle2, Circle } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Link } from "react-router-dom";

interface ProjectCardProps {
  project: {
    id: string;
    name: string;
    company: string;
    description: string;
    status: "active" | "draft" | "completed";
    progress: number;
    lastUpdated: string;
    tags: string[];
    checklist: {
      recording: boolean;
      transcription: boolean;
      brief: boolean;
      workflow: boolean;
      mockups: boolean;
      externals: boolean;
    };
  };
}

export function ProjectCard({ project }: ProjectCardProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-success text-success-foreground";
      case "active":
        return "bg-primary text-primary-foreground";
      case "draft":
        return "bg-muted text-muted-foreground";
      default:
        return "bg-muted text-muted-foreground";
    }
  };

  const completedItems = Object.values(project.checklist).filter(Boolean).length;
  const totalItems = Object.keys(project.checklist).length;

  return (
    <Card className="group hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-card to-card/80 border-border/50">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="space-y-2">
            <CardTitle className="text-lg group-hover:text-primary transition-colors">
              {project.name}
            </CardTitle>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Users className="h-4 w-4" />
              {project.company}
            </div>
          </div>
          <Badge className={getStatusColor(project.status)}>
            {project.status}
          </Badge>
        </div>
        <p className="text-sm text-muted-foreground line-clamp-2">
          {project.description}
        </p>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Completion</span>
            <span className="font-medium">{completedItems}/{totalItems}</span>
          </div>
          <Progress value={(completedItems / totalItems) * 100} className="h-2" />
        </div>

        <div className="flex flex-wrap gap-1">
          {project.tags.map((tag) => (
            <Badge key={tag} variant="outline" className="text-xs">
              {tag}
            </Badge>
          ))}
        </div>

        <div className="grid grid-cols-3 gap-2 text-xs">
          <div className="flex items-center gap-1">
            {project.checklist.recording ? (
              <CheckCircle2 className="h-3 w-3 text-success" />
            ) : (
              <Circle className="h-3 w-3 text-muted-foreground" />
            )}
            <span className="text-muted-foreground">Recording</span>
          </div>
          <div className="flex items-center gap-1">
            {project.checklist.brief ? (
              <CheckCircle2 className="h-3 w-3 text-success" />
            ) : (
              <Circle className="h-3 w-3 text-muted-foreground" />
            )}
            <span className="text-muted-foreground">Brief</span>
          </div>
          <div className="flex items-center gap-1">
            {project.checklist.mockups ? (
              <CheckCircle2 className="h-3 w-3 text-success" />
            ) : (
              <Circle className="h-3 w-3 text-muted-foreground" />
            )}
            <span className="text-muted-foreground">Mockups</span>
          </div>
        </div>

        <div className="flex items-center justify-between pt-2">
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            <CalendarDays className="h-3 w-3" />
            {project.lastUpdated}
          </div>
          <Button variant="outline" size="sm" asChild>
            <Link to={`/project/${project.id}`}>
              <FileText className="h-4 w-4" />
              Open
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}