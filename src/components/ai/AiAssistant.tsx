import { useState } from 'react';
import { Bot, MessageCircle, FileText, CheckCircle, BarChart3, Upload, Spark<PERSON> } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AiConversation } from './AiConversation';
import { 
  useExtractRequirements, 
  useRefineRequirements, 
  useTranscribeAudio, 
  useStartConversation, 
  useProjectConversations,
  useAnalyzeTemplateGaps,
  useSuggestProjectType,
  useValidateRequirements,
  usePerformGapAnalysis,
  useQualityMetrics
} from '@/hooks/useAi';
import { AiConversationRequest } from '@/services/aiService';

interface AiAssistantProps {
  projectId: number;
  projectName: string;
  projectDescription?: string;
}

export function AiAssistant({ projectId, projectName, projectDescription }: AiAssistantProps) {
  const [activeConversation, setActiveConversation] = useState<any>(null);
  const [requirementsText, setRequirementsText] = useState('');
  const [audioFile, setAudioFile] = useState<File | null>(null);
  const [conversationType, setConversationType] = useState<AiConversationRequest['type']>('REQUIREMENTS_GATHERING');
  const [projectType, setProjectType] = useState('');

  const extractRequirements = useExtractRequirements();
  const refineRequirements = useRefineRequirements();
  const transcribeAudio = useTranscribeAudio();
  const startConversation = useStartConversation();
  const { data: conversations = [] } = useProjectConversations(projectId);
  const analyzeTemplateGaps = useAnalyzeTemplateGaps();
  const suggestProjectType = useSuggestProjectType();
  const validateRequirements = useValidateRequirements();
  const performGapAnalysis = usePerformGapAnalysis();
  const { data: qualityMetrics } = useQualityMetrics(projectId);

  const handleExtractRequirements = async () => {
    if (!requirementsText.trim()) return;
    
    try {
      const result = await extractRequirements.mutateAsync(requirementsText);
      console.log('Extracted requirements:', result);
    } catch (error) {
      console.error('Failed to extract requirements:', error);
    }
  };

  const handleRefineRequirements = async () => {
    if (!requirementsText.trim()) return;
    
    try {
      const result = await refineRequirements.mutateAsync(requirementsText);
      console.log('Refined requirements:', result);
    } catch (error) {
      console.error('Failed to refine requirements:', error);
    }
  };

  const handleTranscribeAudio = async () => {
    if (!audioFile) return;
    
    try {
      const result = await transcribeAudio.mutateAsync(audioFile);
      console.log('Transcription result:', result);
      setRequirementsText(result);
    } catch (error) {
      console.error('Failed to transcribe audio:', error);
    }
  };

  const handleStartConversation = async () => {
    try {
      const conversation = await startConversation.mutateAsync({
        projectId,
        type: conversationType,
        initialContext: projectDescription || ''
      });
      setActiveConversation(conversation);
    } catch (error) {
      console.error('Failed to start conversation:', error);
    }
  };

  const handleAnalyzeGaps = async () => {
    if (!requirementsText.trim()) return;
    
    try {
      const result = await analyzeTemplateGaps.mutateAsync(requirementsText);
      console.log('Gap analysis:', result);
    } catch (error) {
      console.error('Failed to analyze gaps:', error);
    }
  };

  const handleSuggestProjectType = async () => {
    if (!projectDescription) return;
    
    try {
      const result = await suggestProjectType.mutateAsync(projectDescription);
      console.log('Project type suggestion:', result);
    } catch (error) {
      console.error('Failed to suggest project type:', error);
    }
  };

  const handleValidateRequirements = async () => {
    try {
      const result = await validateRequirements.mutateAsync(projectId);
      console.log('Validation result:', result);
    } catch (error) {
      console.error('Failed to validate requirements:', error);
    }
  };

  const handleGapAnalysis = async () => {
    if (!projectType) return;
    
    try {
      const result = await performGapAnalysis.mutateAsync({
        projectId,
        projectType,
        industryStandards: 'Standard software development practices'
      });
      console.log('Gap analysis result:', result);
    } catch (error) {
      console.error('Failed to perform gap analysis:', error);
    }
  };

  if (activeConversation) {
    return (
      <AiConversation
        conversation={activeConversation}
        onClose={() => setActiveConversation(null)}
      />
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bot className="h-5 w-5" />
          AI Assistant
        </CardTitle>
      </CardHeader>
      
      <CardContent>
        <Tabs defaultValue="conversations" className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="conversations">Chat</TabsTrigger>
            <TabsTrigger value="extract">Extract</TabsTrigger>
            <TabsTrigger value="analyze">Analyze</TabsTrigger>
            <TabsTrigger value="quality">Quality</TabsTrigger>
          </TabsList>
          
          <TabsContent value="conversations" className="space-y-4">
            <div className="space-y-4">
              <div>
                <Label htmlFor="conversation-type">Conversation Type</Label>
                <Select value={conversationType} onValueChange={(value: any) => setConversationType(value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select conversation type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="REQUIREMENTS_GATHERING">Requirements Gathering</SelectItem>
                    <SelectItem value="REQUIREMENTS_REFINEMENT">Requirements Refinement</SelectItem>
                    <SelectItem value="GAP_ANALYSIS">Gap Analysis</SelectItem>
                    <SelectItem value="STAKEHOLDER_INTERVIEW">Stakeholder Interview</SelectItem>
                    <SelectItem value="TECHNICAL_CLARIFICATION">Technical Clarification</SelectItem>
                    <SelectItem value="VALIDATION_REVIEW">Validation Review</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <Button 
                onClick={handleStartConversation}
                disabled={startConversation.isPending}
                className="w-full"
              >
                <MessageCircle className="h-4 w-4 mr-2" />
                Start AI Conversation
              </Button>

              {conversations.length > 0 && (
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Recent Conversations</h4>
                  {conversations.slice(0, 3).map((conv) => (
                    <Card key={conv.id} className="p-3 cursor-pointer hover:bg-muted/50" onClick={() => setActiveConversation(conv)}>
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <p className="text-sm font-medium">{conv.title}</p>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge variant="outline" className="text-xs">
                              {conv.type.replace(/_/g, ' ')}
                            </Badge>
                            <Badge 
                              variant={conv.status === 'ACTIVE' ? 'default' : 'secondary'}
                              className="text-xs"
                            >
                              {conv.status}
                            </Badge>
                          </div>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {Math.round(conv.completionPercentage)}%
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>
          
          <TabsContent value="extract" className="space-y-4">
            <div className="space-y-4">
              <div>
                <Label htmlFor="audio-upload">Upload Audio Recording</Label>
                <Input
                  id="audio-upload"
                  type="file"
                  accept="audio/*"
                  onChange={(e) => setAudioFile(e.target.files?.[0] || null)}
                />
                <Button 
                  onClick={handleTranscribeAudio}
                  disabled={!audioFile || transcribeAudio.isPending}
                  className="w-full mt-2"
                  variant="outline"
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Transcribe Audio
                </Button>
              </div>

              <div>
                <Label htmlFor="requirements-text">Requirements Text</Label>
                <Textarea
                  id="requirements-text"
                  value={requirementsText}
                  onChange={(e) => setRequirementsText(e.target.value)}
                  placeholder="Paste meeting notes, requirements, or any project text here..."
                  className="min-h-[120px]"
                />
              </div>

              <div className="grid grid-cols-2 gap-2">
                <Button 
                  onClick={handleExtractRequirements}
                  disabled={!requirementsText.trim() || extractRequirements.isPending}
                  variant="outline"
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Extract Requirements
                </Button>
                <Button 
                  onClick={handleRefineRequirements}
                  disabled={!requirementsText.trim() || refineRequirements.isPending}
                  variant="outline"
                >
                  <Sparkles className="h-4 w-4 mr-2" />
                  Refine Requirements
                </Button>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="analyze" className="space-y-4">
            <div className="space-y-4">
              <div>
                <Label htmlFor="project-type">Project Type</Label>
                <Input
                  id="project-type"
                  value={projectType}
                  onChange={(e) => setProjectType(e.target.value)}
                  placeholder="e.g., Web Application, Mobile App, Enterprise System"
                />
                <Button 
                  onClick={handleSuggestProjectType}
                  disabled={!projectDescription || suggestProjectType.isPending}
                  className="w-full mt-2"
                  variant="outline"
                  size="sm"
                >
                  Suggest Project Type
                </Button>
              </div>

              <div className="grid grid-cols-1 gap-2">
                <Button 
                  onClick={handleAnalyzeGaps}
                  disabled={!requirementsText.trim() || analyzeTemplateGaps.isPending}
                  variant="outline"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Analyze Template Gaps
                </Button>
                <Button 
                  onClick={handleValidateRequirements}
                  disabled={validateRequirements.isPending}
                  variant="outline"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Validate Requirements
                </Button>
                <Button 
                  onClick={handleGapAnalysis}
                  disabled={!projectType || performGapAnalysis.isPending}
                  variant="outline"
                >
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Perform Gap Analysis
                </Button>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="quality" className="space-y-4">
            <div className="space-y-4">
              <h4 className="text-sm font-medium">Quality Metrics</h4>
              {qualityMetrics ? (
                <div className="grid grid-cols-2 gap-4">
                  <Card className="p-3">
                    <div className="text-2xl font-bold">85%</div>
                    <div className="text-xs text-muted-foreground">Completeness</div>
                  </Card>
                  <Card className="p-3">
                    <div className="text-2xl font-bold">92%</div>
                    <div className="text-xs text-muted-foreground">Clarity</div>
                  </Card>
                  <Card className="p-3">
                    <div className="text-2xl font-bold">78%</div>
                    <div className="text-xs text-muted-foreground">Testability</div>
                  </Card>
                  <Card className="p-3">
                    <div className="text-2xl font-bold">88%</div>
                    <div className="text-xs text-muted-foreground">Overall Score</div>
                  </Card>
                </div>
              ) : (
                <div className="text-center text-muted-foreground py-8">
                  No quality metrics available yet. Add some requirements to get started.
                </div>
              )}
              
              <div className="space-y-2">
                <h5 className="text-sm font-medium">Recommendations</h5>
                <div className="space-y-1 text-sm text-muted-foreground">
                  <p>• Add acceptance criteria to 5 requirements</p>
                  <p>• Clarify performance requirements</p>
                  <p>• Define security requirements</p>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}