import { useState, useRef, useEffect } from 'react';
import { Bo<PERSON>, User, Send, Pause, Play, Square, Loader2 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useConversationMessages, useSendMessage, useConversationActions } from '@/hooks/useAi';
import { AiConversation as AiConversationType, AiMessage } from '@/services/aiService';
import { formatDistanceToNow } from 'date-fns';

interface AiConversationProps {
  conversation: AiConversationType;
  onClose?: () => void;
}

export function AiConversation({ conversation, onClose }: AiConversationProps) {
  const [message, setMessage] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  const { data: messages = [], isLoading } = useConversationMessages(conversation.id);
  const sendMessage = useSendMessage();
  const { pauseConversation, resumeConversation, completeConversation } = useConversationActions();

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!message.trim() || sendMessage.isPending) return;

    const messageToSend = message.trim();
    setMessage('');

    try {
      await sendMessage.mutateAsync({
        conversationId: conversation.id,
        message: messageToSend
      });
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-500';
      case 'PAUSED':
        return 'bg-yellow-500';
      case 'COMPLETED':
        return 'bg-blue-500';
      case 'ARCHIVED':
        return 'bg-gray-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getTypeLabel = (type: string) => {
    return type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  return (
    <Card className="h-[600px] flex flex-col">
      <CardHeader className="flex-shrink-0 pb-3">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <CardTitle className="text-lg">{conversation.title}</CardTitle>
            <div className="flex items-center gap-2">
              <Badge variant="outline">{getTypeLabel(conversation.type)}</Badge>
              <Badge className={getStatusColor(conversation.status)}>
                {conversation.status}
              </Badge>
              {conversation.completionPercentage > 0 && (
                <Badge variant="secondary">
                  {Math.round(conversation.completionPercentage)}% Complete
                </Badge>
              )}
            </div>
          </div>
          <div className="flex items-center gap-2">
            {conversation.status === 'ACTIVE' && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => pauseConversation.mutate(conversation.id)}
                disabled={pauseConversation.isPending}
              >
                <Pause className="h-4 w-4" />
              </Button>
            )}
            {conversation.status === 'PAUSED' && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => resumeConversation.mutate(conversation.id)}
                disabled={resumeConversation.isPending}
              >
                <Play className="h-4 w-4" />
              </Button>
            )}
            {['ACTIVE', 'PAUSED'].includes(conversation.status) && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => completeConversation.mutate(conversation.id)}
                disabled={completeConversation.isPending}
              >
                <Square className="h-4 w-4" />
              </Button>
            )}
            {onClose && (
              <Button variant="ghost" size="sm" onClick={onClose}>
                ×
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col gap-4 p-4">
        <ScrollArea className="flex-1 pr-4">
          {isLoading ? (
            <div className="flex items-center justify-center h-32">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <div className="space-y-4">
              {messages.map((msg: AiMessage) => (
                <div
                  key={msg.id}
                  className={`flex gap-3 ${
                    msg.role === 'USER' ? 'justify-end' : 'justify-start'
                  }`}
                >
                  {msg.role !== 'USER' && (
                    <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                      <Bot className="h-4 w-4 text-primary" />
                    </div>
                  )}
                  
                  <div
                    className={`max-w-[80%] rounded-lg p-3 ${
                      msg.role === 'USER'
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-muted'
                    }`}
                  >
                    <div className="whitespace-pre-wrap text-sm">
                      {msg.content}
                    </div>
                    <div className="text-xs opacity-70 mt-2">
                      {formatDistanceToNow(new Date(msg.createdAt), { addSuffix: true })}
                      {msg.processingTimeMs && (
                        <span className="ml-2">
                          ({msg.processingTimeMs}ms)
                        </span>
                      )}
                    </div>
                  </div>

                  {msg.role === 'USER' && (
                    <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                      <User className="h-4 w-4 text-primary" />
                    </div>
                  )}
                </div>
              ))}
              <div ref={messagesEndRef} />
            </div>
          )}
        </ScrollArea>

        {conversation.status === 'ACTIVE' && (
          <div className="flex gap-2">
            <Textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type your message here..."
              className="min-h-[60px] resize-none"
              disabled={sendMessage.isPending}
            />
            <Button
              onClick={handleSendMessage}
              disabled={!message.trim() || sendMessage.isPending}
              className="px-3"
            >
              {sendMessage.isPending ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
        )}

        {conversation.status !== 'ACTIVE' && (
          <div className="text-center text-sm text-muted-foreground p-4 bg-muted rounded-lg">
            This conversation is {conversation.status.toLowerCase()}.
            {conversation.status === 'PAUSED' && ' Resume to continue chatting.'}
          </div>
        )}
      </CardContent>
    </Card>
  );
}