import { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, Eye, Edit3, Save, Download } from "lucide-react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, Ta<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";

interface MarkdownEditorProps {
  initialContent?: string;
  onSave?: (content: string) => void;
  title?: string;
}

export function MarkdownEditor({ 
  initialContent = "", 
  onSave, 
  title = "Project Brief" 
}: MarkdownEditorProps) {
  const [content, setContent] = useState(initialContent);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [activeTab, setActiveTab] = useState("edit");

  const handleSave = () => {
    onSave?.(content);
    setLastSaved(new Date());
  };

  const handleExport = () => {
    const blob = new Blob([content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${title.toLowerCase().replace(/\s+/g, '-')}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Simple markdown to HTML conversion for preview
  const convertToHtml = (markdown: string) => {
    return markdown
      .replace(/^# (.*$)/gm, '<h1>$1</h1>')
      .replace(/^## (.*$)/gm, '<h2>$1</h2>')
      .replace(/^### (.*$)/gm, '<h3>$1</h3>')
      .replace(/\*\*(.*)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*)\*/g, '<em>$1</em>')
      .replace(/\n/g, '<br>');
  };

  const defaultTemplate = `# ${title}

## Project Overview
Brief description of the project scope and objectives.

## Requirements

### Functional Requirements
- Feature 1: Description
- Feature 2: Description
- Feature 3: Description

### Non-Functional Requirements
- Performance requirements
- Security requirements
- Scalability requirements

## User Stories
As a [user type], I want [functionality] so that [benefit].

## Technical Specifications

### Technology Stack
- Frontend: 
- Backend: 
- Database: 
- Infrastructure: 

### APIs and Integrations
- Third-party service 1
- Third-party service 2

## UI/UX Requirements

### Design Guidelines
- Design system requirements
- Accessibility requirements
- Responsive design requirements

### User Flow
Description of the main user journey through the application.

## Timeline and Milestones
- Phase 1: [Description] - [Date]
- Phase 2: [Description] - [Date]
- Phase 3: [Description] - [Date]

## Success Criteria
How will we measure the success of this project?

## Risks and Assumptions
- Risk 1: Description and mitigation strategy
- Assumption 1: Description

## Additional Notes
Any other relevant information or considerations.
`;

  return (
    <Card className="h-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {title}
          </CardTitle>
          <div className="flex items-center gap-2">
            {lastSaved && (
              <Badge variant="outline" className="text-xs">
                Saved {lastSaved.toLocaleTimeString()}
              </Badge>
            )}
            <Button variant="outline" size="sm" onClick={handleSave}>
              <Save className="h-4 w-4" />
              Save
            </Button>
            <Button variant="ghost" size="sm" onClick={handleExport}>
              <Download className="h-4 w-4" />
              Export
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-0">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
          <div className="px-6 border-b">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="edit" className="flex items-center gap-2">
                <Edit3 className="h-4 w-4" />
                Edit
              </TabsTrigger>
              <TabsTrigger value="preview" className="flex items-center gap-2">
                <Eye className="h-4 w-4" />
                Preview
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="edit" className="mt-0 h-full">
            <div className="p-6">
              {content === "" && (
                <div className="mb-4 p-4 bg-muted/50 rounded-lg">
                  <p className="text-sm text-muted-foreground mb-2">
                    Start with a template or write your own content:
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setContent(defaultTemplate)}
                  >
                    Use Template
                  </Button>
                </div>
              )}
              <Textarea
                value={content}
                onChange={(e) => setContent(e.target.value)}
                placeholder="Write your project brief in Markdown format..."
                className="min-h-[500px] font-mono text-sm resize-none border-0 focus-visible:ring-0"
              />
            </div>
          </TabsContent>

          <TabsContent value="preview" className="mt-0 h-full">
            <div className="p-6">
              {content ? (
                <div 
                  className="prose prose-sm max-w-none"
                  dangerouslySetInnerHTML={{ __html: convertToHtml(content) }}
                />
              ) : (
                <div className="text-center py-12 text-muted-foreground">
                  <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No content to preview yet.</p>
                  <p className="text-sm">Switch to edit mode to start writing.</p>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}