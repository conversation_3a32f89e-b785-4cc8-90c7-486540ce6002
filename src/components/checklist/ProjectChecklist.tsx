import { CheckCircle2, <PERSON>, Clock, AlertCircle } from "lucide-react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface ChecklistItem {
  id: string;
  title: string;
  description: string;
  completed: boolean;
  required: boolean;
  canOverride?: boolean;
}

interface ProjectChecklistProps {
  checklist: ChecklistItem[];
  onToggle: (itemId: string, completed: boolean) => void;
  onFinalize: () => void;
}

export function ProjectChecklist({ checklist, onToggle, onFinalize }: ProjectChecklistProps) {
  const completedItems = checklist.filter(item => item.completed).length;
  const requiredItems = checklist.filter(item => item.required);
  const completedRequired = requiredItems.filter(item => item.completed).length;
  const totalItems = checklist.length;
  const canFinalize = completedRequired === requiredItems.length;

  const getItemStatus = (item: ChecklistItem) => {
    if (item.completed) return "completed";
    if (item.required) return "required";
    return "optional";
  };

  const getItemIcon = (item: ChecklistItem) => {
    if (item.completed) {
      return <CheckCircle2 className="h-5 w-5 text-success" />;
    }
    if (item.required) {
      return <AlertCircle className="h-5 w-5 text-warning" />;
    }
    return <Circle className="h-5 w-5 text-muted-foreground" />;
  };

  const getItemBadge = (item: ChecklistItem) => {
    const status = getItemStatus(item);
    switch (status) {
      case "completed":
        return <Badge variant="default" className="bg-success text-success-foreground">Complete</Badge>;
      case "required":
        return <Badge variant="default" className="bg-warning text-warning-foreground">Required</Badge>;
      case "optional":
        return <Badge variant="outline">Optional</Badge>;
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Project Completion Checklist
            </CardTitle>
            <CardDescription>
              Complete all required items before finalizing the PRD
            </CardDescription>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold">
              {completedItems}/{totalItems}
            </div>
            <p className="text-sm text-muted-foreground">Items completed</p>
          </div>
        </div>
        
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Overall Progress</span>
            <span>{Math.round((completedItems / totalItems) * 100)}%</span>
          </div>
          <Progress value={(completedItems / totalItems) * 100} className="h-2" />
          
          <div className="flex justify-between text-sm">
            <span>Required Items</span>
            <span className={cn(
              completedRequired === requiredItems.length ? "text-success" : "text-warning"
            )}>
              {completedRequired}/{requiredItems.length}
            </span>
          </div>
          <Progress 
            value={(completedRequired / requiredItems.length) * 100} 
            className="h-2" 
          />
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="space-y-3">
          {checklist.map((item) => (
            <div
              key={item.id}
              className={cn(
                "flex items-start gap-4 p-4 rounded-lg border transition-all duration-200",
                item.completed 
                  ? "bg-success/5 border-success/20" 
                  : "bg-card hover:bg-muted/30"
              )}
            >
              <button
                onClick={() => onToggle(item.id, !item.completed)}
                className="mt-0.5 hover:scale-110 transition-transform"
              >
                {getItemIcon(item)}
              </button>
              
              <div className="flex-1 space-y-1">
                <div className="flex items-center justify-between">
                  <h4 className={cn(
                    "font-medium",
                    item.completed && "line-through text-muted-foreground"
                  )}>
                    {item.title}
                  </h4>
                  {getItemBadge(item)}
                </div>
                <p className="text-sm text-muted-foreground">
                  {item.description}
                </p>
                
                {item.canOverride && !item.completed && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onToggle(item.id, true)}
                    className="text-xs mt-2"
                  >
                    Mark as N/A
                  </Button>
                )}
              </div>
            </div>
          ))}
        </div>

        <div className="pt-4 border-t">
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              variant="gradient"
              disabled={!canFinalize}
              onClick={onFinalize}
              className="flex-1"
            >
              {canFinalize ? "Finalize PRD" : `Complete ${requiredItems.length - completedRequired} more required items`}
            </Button>
            <Button variant="outline" className="sm:w-auto">
              Export Draft
            </Button>
          </div>
          
          {!canFinalize && (
            <p className="text-sm text-muted-foreground mt-2 text-center">
              Complete all required items to finalize the Project Requirements Document
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}