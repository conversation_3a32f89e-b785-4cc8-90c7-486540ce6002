import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { aiService, AiConversationRequest, AiConversation, AiMessage } from '@/services/aiService';
import { toast } from 'sonner';

// Requirements Extraction Hooks
export const useExtractRequirements = () => {
  return useMutation({
    mutationFn: aiService.extractRequirements,
    onError: (error: any) => {
      toast.error('Failed to extract requirements', {
        description: error.response?.data?.message || 'An unexpected error occurred'
      });
    },
    onSuccess: () => {
      toast.success('Requirements extracted successfully');
    }
  });
};

export const useRefineRequirements = () => {
  return useMutation({
    mutationFn: aiService.refineRequirements,
    onError: (error: any) => {
      toast.error('Failed to refine requirements', {
        description: error.response?.data?.message || 'An unexpected error occurred'
      });
    },
    onSuccess: () => {
      toast.success('Requirements refined successfully');
    }
  });
};

export const useGenerateQuestions = () => {
  return useMutation({
    mutationFn: aiService.generateQuestions,
    onError: (error: any) => {
      toast.error('Failed to generate questions', {
        description: error.response?.data?.message || 'An unexpected error occurred'
      });
    }
  });
};

// Audio Processing Hooks
export const useTranscribeAudio = () => {
  return useMutation({
    mutationFn: aiService.transcribeAudio,
    onError: (error: any) => {
      toast.error('Failed to transcribe audio', {
        description: error.response?.data?.message || 'An unexpected error occurred'
      });
    },
    onSuccess: () => {
      toast.success('Audio transcribed successfully');
    }
  });
};

export const useAnalyzeMeeting = () => {
  return useMutation({
    mutationFn: aiService.analyzeMeeting,
    onError: (error: any) => {
      toast.error('Failed to analyze meeting', {
        description: error.response?.data?.message || 'An unexpected error occurred'
      });
    },
    onSuccess: () => {
      toast.success('Meeting analyzed successfully');
    }
  });
};

// AI Conversation Hooks
export const useStartConversation = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: aiService.startConversation,
    onError: (error: any) => {
      toast.error('Failed to start AI conversation', {
        description: error.response?.data?.message || 'An unexpected error occurred'
      });
    },
    onSuccess: (data: AiConversation) => {
      toast.success('AI conversation started');
      queryClient.invalidateQueries({ queryKey: ['ai-conversations', data.projectId] });
    }
  });
};

export const useSendMessage = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ conversationId, message }: { conversationId: number; message: string }) =>
      aiService.sendMessage(conversationId, message),
    onError: (error: any) => {
      toast.error('Failed to send message', {
        description: error.response?.data?.message || 'An unexpected error occurred'
      });
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['ai-messages', variables.conversationId] });
    }
  });
};

export const useConversationMessages = (conversationId: number) => {
  return useQuery({
    queryKey: ['ai-messages', conversationId],
    queryFn: () => aiService.getConversationMessages(conversationId),
    enabled: !!conversationId,
    refetchInterval: 5000, // Refresh every 5 seconds
  });
};

export const useProjectConversations = (projectId: number) => {
  return useQuery({
    queryKey: ['ai-conversations', projectId],
    queryFn: () => aiService.getProjectConversations(projectId),
    enabled: !!projectId,
  });
};

export const useConversationActions = () => {
  const queryClient = useQueryClient();
  
  const pauseConversation = useMutation({
    mutationFn: aiService.pauseConversation,
    onSuccess: () => {
      toast.success('Conversation paused');
      queryClient.invalidateQueries({ queryKey: ['ai-conversations'] });
    },
    onError: (error: any) => {
      toast.error('Failed to pause conversation', {
        description: error.response?.data?.message || 'An unexpected error occurred'
      });
    }
  });

  const resumeConversation = useMutation({
    mutationFn: aiService.resumeConversation,
    onSuccess: () => {
      toast.success('Conversation resumed');
      queryClient.invalidateQueries({ queryKey: ['ai-conversations'] });
    },
    onError: (error: any) => {
      toast.error('Failed to resume conversation', {
        description: error.response?.data?.message || 'An unexpected error occurred'
      });
    }
  });

  const completeConversation = useMutation({
    mutationFn: aiService.completeConversation,
    onSuccess: () => {
      toast.success('Conversation completed');
      queryClient.invalidateQueries({ queryKey: ['ai-conversations'] });
    },
    onError: (error: any) => {
      toast.error('Failed to complete conversation', {
        description: error.response?.data?.message || 'An unexpected error occurred'
      });
    }
  });

  return {
    pauseConversation,
    resumeConversation,
    completeConversation
  };
};

// Template Analysis Hooks
export const useAnalyzeTemplateGaps = () => {
  return useMutation({
    mutationFn: aiService.analyzeTemplateGaps,
    onError: (error: any) => {
      toast.error('Failed to analyze template gaps', {
        description: error.response?.data?.message || 'An unexpected error occurred'
      });
    }
  });
};

export const useSuggestProjectType = () => {
  return useMutation({
    mutationFn: aiService.suggestProjectType,
    onError: (error: any) => {
      toast.error('Failed to suggest project type', {
        description: error.response?.data?.message || 'An unexpected error occurred'
      });
    }
  });
};

export const useGenerateChecklist = () => {
  return useMutation({
    mutationFn: ({ projectType, projectContext }: { projectType: string; projectContext: string }) =>
      aiService.generateChecklist(projectType, projectContext),
    onError: (error: any) => {
      toast.error('Failed to generate checklist', {
        description: error.response?.data?.message || 'An unexpected error occurred'
      });
    }
  });
};

// Gap Analysis Hooks
export const useValidateRequirements = () => {
  return useMutation({
    mutationFn: aiService.validateRequirements,
    onError: (error: any) => {
      toast.error('Failed to validate requirements', {
        description: error.response?.data?.message || 'An unexpected error occurred'
      });
    }
  });
};

export const usePerformGapAnalysis = () => {
  return useMutation({
    mutationFn: ({ projectId, projectType, industryStandards }: { 
      projectId: number; 
      projectType: string; 
      industryStandards?: string;
    }) => aiService.performGapAnalysis(projectId, projectType, industryStandards),
    onError: (error: any) => {
      toast.error('Failed to perform gap analysis', {
        description: error.response?.data?.message || 'An unexpected error occurred'
      });
    }
  });
};

export const useQualityMetrics = (projectId: number) => {
  return useQuery({
    queryKey: ['quality-metrics', projectId],
    queryFn: () => aiService.getQualityMetrics(projectId),
    enabled: !!projectId,
  });
};