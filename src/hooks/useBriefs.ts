import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { briefService } from '@/services/briefService';
import { Brief, CreateBriefRequest, PageRequest } from '@/types/api';
import { toast } from 'sonner';

export const useBriefs = (projectId: string) => {
  return useQuery({
    queryKey: ['briefs', projectId],
    queryFn: () => briefService.getBriefs(projectId),
    enabled: !!projectId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useBriefsPaginated = (projectId: string, params?: PageRequest) => {
  return useQuery({
    queryKey: ['briefs', projectId, 'paginated', params],
    queryFn: () => briefService.getBriefsPaginated(projectId, params),
    enabled: !!projectId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useBrief = (projectId: string, briefId: string) => {
  return useQuery({
    queryKey: ['brief', projectId, briefId],
    queryFn: () => briefService.getBrief(projectId, briefId),
    enabled: !!projectId && !!briefId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useCreateBrief = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ projectId, data }: { projectId: string; data: CreateBriefRequest }) => 
      briefService.createBrief(projectId, data),
    onSuccess: (newBrief: Brief) => {
      queryClient.invalidateQueries({ queryKey: ['briefs', newBrief.projectId] });
      queryClient.invalidateQueries({ queryKey: ['project', newBrief.projectId] });
      toast.success('Brief created successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create brief');
    },
  });
};

export const useUpdateBrief = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ projectId, briefId, data }: { 
      projectId: string; 
      briefId: string; 
      data: CreateBriefRequest 
    }) => briefService.updateBrief(projectId, briefId, data),
    onSuccess: (updatedBrief: Brief) => {
      queryClient.invalidateQueries({ queryKey: ['briefs', updatedBrief.projectId] });
      queryClient.invalidateQueries({ queryKey: ['brief', updatedBrief.projectId, updatedBrief.id] });
      toast.success('Brief updated successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update brief');
    },
  });
};

export const useFinalizeBrief = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ projectId, briefId }: { projectId: string; briefId: string }) => 
      briefService.finalizeBrief(projectId, briefId),
    onSuccess: (finalizedBrief: Brief) => {
      queryClient.invalidateQueries({ queryKey: ['briefs', finalizedBrief.projectId] });
      queryClient.invalidateQueries({ queryKey: ['brief', finalizedBrief.projectId, finalizedBrief.id] });
      queryClient.invalidateQueries({ queryKey: ['project', finalizedBrief.projectId] });
      toast.success('Brief finalized successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to finalize brief');
    },
  });
};

export const useDeleteBrief = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ projectId, briefId }: { projectId: string; briefId: string }) => 
      briefService.deleteBrief(projectId, briefId),
    onSuccess: (_, { projectId }) => {
      queryClient.invalidateQueries({ queryKey: ['briefs', projectId] });
      queryClient.invalidateQueries({ queryKey: ['project', projectId] });
      toast.success('Brief deleted successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete brief');
    },
  });
};