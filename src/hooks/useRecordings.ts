import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { recordingService } from '@/services/recordingService';
import { Recording, UpdateRecordingRequest, PageRequest } from '@/types/api';
import { toast } from 'sonner';

export const useRecordings = (projectId: string) => {
  return useQuery({
    queryKey: ['recordings', projectId],
    queryFn: () => recordingService.getRecordings(projectId),
    enabled: !!projectId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useRecordingsPaginated = (projectId: string, params?: PageRequest) => {
  return useQuery({
    queryKey: ['recordings', projectId, 'paginated', params],
    queryFn: () => recordingService.getRecordingsPaginated(projectId, params),
    enabled: !!projectId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useRecording = (projectId: string, recordingId: string) => {
  return useQuery({
    queryKey: ['recording', projectId, recordingId],
    queryFn: () => recordingService.getRecording(projectId, recordingId),
    enabled: !!projectId && !!recordingId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useCreateRecording = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ projectId, file, title }: { projectId: string; file: File; title: string }) => 
      recordingService.createRecording(projectId, file, title),
    onSuccess: (newRecording: Recording) => {
      queryClient.invalidateQueries({ queryKey: ['recordings', newRecording.projectId] });
      queryClient.invalidateQueries({ queryKey: ['project', newRecording.projectId] });
      toast.success('Recording uploaded successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to upload recording');
    },
  });
};

export const useUpdateRecording = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ projectId, recordingId, data }: { 
      projectId: string; 
      recordingId: string; 
      data: UpdateRecordingRequest 
    }) => recordingService.updateRecording(projectId, recordingId, data),
    onSuccess: (updatedRecording: Recording) => {
      queryClient.invalidateQueries({ queryKey: ['recordings', updatedRecording.projectId] });
      queryClient.invalidateQueries({ queryKey: ['recording', updatedRecording.projectId, updatedRecording.id] });
      toast.success('Recording updated successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update recording');
    },
  });
};

export const useDeleteRecording = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ projectId, recordingId }: { projectId: string; recordingId: string }) => 
      recordingService.deleteRecording(projectId, recordingId),
    onSuccess: (_, { projectId }) => {
      queryClient.invalidateQueries({ queryKey: ['recordings', projectId] });
      queryClient.invalidateQueries({ queryKey: ['project', projectId] });
      toast.success('Recording deleted successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete recording');
    },
  });
};