import { useEffect, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import websocketService from '@/services/websocketService';
import { useAuth } from '@/contexts/AuthContext';
import { WebSocketMessage } from '@/types/api';
import { toast } from 'sonner';

export const useWebSocketConnection = () => {
  const { isAuthenticated, user } = useAuth();
  const queryClient = useQueryClient();
  const connectionRef = useRef<boolean>(false);

  useEffect(() => {
    if (isAuthenticated && user && !connectionRef.current) {
      // Connect to WebSocket
      websocketService.connect();
      connectionRef.current = true;

      // Subscribe to user notifications
      websocketService.subscribeToNotifications(user.id, (message: WebSocketMessage) => {
        console.log('User notification:', message);
        
        // Show toast notification
        toast.info('Notification', {
          description: message.data?.message || 'You have a new notification',
        });
      });

      return () => {
        websocketService.disconnect();
        connectionRef.current = false;
      };
    }
  }, [isAuthenticated, user]);

  return {
    isConnected: websocketService.isConnectedToServer(),
  };
};

export const useProjectWebSocket = (projectId: string) => {
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!projectId) return;

    // Subscribe to project updates
    websocketService.subscribeToProject(projectId, (message: WebSocketMessage) => {
      console.log('Project update:', message);
      
      // Invalidate project queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['project', projectId] });
      
      // Show notification based on message type
      switch (message.type) {
        case 'project_updated':
          toast.success('Project updated');
          break;
        case 'project_status_changed':
          toast.info('Project status changed');
          break;
        default:
          break;
      }
    });

    // Subscribe to recording updates
    websocketService.subscribeToRecordings(projectId, (message: WebSocketMessage) => {
      console.log('Recording update:', message);
      
      // Invalidate recording queries
      queryClient.invalidateQueries({ queryKey: ['recordings', projectId] });
      
      switch (message.type) {
        case 'recording_uploaded':
          toast.success('New recording uploaded');
          break;
        case 'recording_transcribed':
          toast.success('Recording transcription completed');
          break;
        case 'recording_deleted':
          toast.info('Recording deleted');
          break;
        default:
          break;
      }
    });

    // Subscribe to brief updates
    websocketService.subscribeToBriefs(projectId, (message: WebSocketMessage) => {
      console.log('Brief update:', message);
      
      // Invalidate brief queries
      queryClient.invalidateQueries({ queryKey: ['briefs', projectId] });
      
      switch (message.type) {
        case 'brief_created':
          toast.success('New brief created');
          break;
        case 'brief_updated':
          toast.info('Brief updated');
          break;
        case 'brief_finalized':
          toast.success('Brief finalized');
          break;
        case 'brief_deleted':
          toast.info('Brief deleted');
          break;
        default:
          break;
      }
    });

    // Cleanup subscriptions when component unmounts or projectId changes
    return () => {
      websocketService.unsubscribeFromProject(projectId);
    };
  }, [projectId, queryClient]);
};

export const useRecordingWebSocket = (projectId: string) => {
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!projectId) return;

    websocketService.subscribeToRecordings(projectId, (message: WebSocketMessage) => {
      // Invalidate recording queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['recordings', projectId] });
      
      // Also invalidate project data to update counts
      queryClient.invalidateQueries({ queryKey: ['project', projectId] });
      queryClient.invalidateQueries({ queryKey: ['projects'] });
    });

    return () => {
      websocketService.unsubscribe(`/topic/project/${projectId}/recordings`);
    };
  }, [projectId, queryClient]);
};

export const useBriefWebSocket = (projectId: string) => {
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!projectId) return;

    websocketService.subscribeToBriefs(projectId, (message: WebSocketMessage) => {
      // Invalidate brief queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['briefs', projectId] });
      
      // Also invalidate project data to update counts
      queryClient.invalidateQueries({ queryKey: ['project', projectId] });
      queryClient.invalidateQueries({ queryKey: ['projects'] });
    });

    return () => {
      websocketService.unsubscribe(`/topic/project/${projectId}/briefs`);
    };
  }, [projectId, queryClient]);
};