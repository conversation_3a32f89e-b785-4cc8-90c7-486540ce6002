package com.projectbriefbuddy.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.projectbriefbuddy.dto.CreateProjectRequest;
import com.projectbriefbuddy.dto.AuthenticationRequest;
import com.projectbriefbuddy.dto.RegisterRequest;
import com.projectbriefbuddy.entity.User;
import com.projectbriefbuddy.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.http.*;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
@Transactional
public class ProjectBriefBuddyIntegrationTest {

    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private UserRepository userRepository;

    @LocalServerPort
    private int port;

    private String accessToken;

    @BeforeEach
    void setUp() throws Exception {
        // Clean up any existing test user
        userRepository.findByEmail("<EMAIL>").ifPresent(userRepository::delete);
        
        // Register a test user
        RegisterRequest registerRequest = new RegisterRequest();
        registerRequest.setFullName("Test User");
        registerRequest.setEmail("<EMAIL>");
        registerRequest.setPassword("password123");

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<RegisterRequest> registerEntity = new HttpEntity<>(registerRequest, headers);

        ResponseEntity<String> registerResponse = restTemplate.postForEntity(
                "http://localhost:" + port + "/api/auth/register",
                registerEntity,
                String.class);

        // Registration might fail if user already exists, which is fine for testing
        assert registerResponse.getStatusCode() == HttpStatus.OK || registerResponse.getStatusCode() == HttpStatus.BAD_REQUEST;

        // Login to get access token
        AuthenticationRequest loginRequest = new AuthenticationRequest();
        loginRequest.setEmail("<EMAIL>");
        loginRequest.setPassword("password123");

        HttpEntity<AuthenticationRequest> loginEntity = new HttpEntity<>(loginRequest, headers);
        ResponseEntity<String> loginResponse = restTemplate.postForEntity(
                "http://localhost:" + port + "/api/auth/login",
                loginEntity,
                String.class);

        assert loginResponse.getStatusCode() == HttpStatus.OK;

        // Extract access token from response (simplified for test)
        accessToken = extractAccessToken(loginResponse.getBody());
    }

    @Test
    void testUserRegistrationAndLogin() throws Exception {
        // Verify user exists in database
        User user = userRepository.findByEmail("<EMAIL>").orElse(null);
        assert user != null;
        assert user.getFullName().equals("Test User");
        assert user.getEmail().equals("<EMAIL>");
        assert user.isActive();
    }

    @Test
    void testProjectCreationAndRetrieval() throws Exception {
        // Create a project
        CreateProjectRequest createRequest = new CreateProjectRequest();
        createRequest.setName("Integration Test Project");
        createRequest.setDescription("A project created during integration testing");

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(accessToken);
        HttpEntity<CreateProjectRequest> createEntity = new HttpEntity<>(createRequest, headers);

        ResponseEntity<String> createResponse = restTemplate.postForEntity(
                "http://localhost:" + port + "/api/projects",
                createEntity,
                String.class);

        assert createResponse.getStatusCode() == HttpStatus.OK;
        assert createResponse.getBody().contains("Integration Test Project");
        assert createResponse.getBody().contains("PLANNING");

        // Get all projects
        HttpEntity<Void> getEntity = new HttpEntity<>(headers);
        ResponseEntity<String> getResponse = restTemplate.exchange(
                "http://localhost:" + port + "/api/projects",
                HttpMethod.GET,
                getEntity,
                String.class);

        assert getResponse.getStatusCode() == HttpStatus.OK;
        assert getResponse.getBody().contains("Integration Test Project");
    }

    @Test
    void testDatabaseConnectivity() throws Exception {
        // Verify database connection by checking user count
        long userCount = userRepository.count();
        assert userCount >= 1; // At least our test user should exist
    }

    private String extractAccessToken(String response) {
        try {
            // Simple extraction - in a real test you'd use proper JSON parsing
            int start = response.indexOf("\"access_token\":\"") + 16;
            int end = response.indexOf("\"", start);
            return response.substring(start, end);
        } catch (Exception e) {
            throw new RuntimeException("Failed to extract access token", e);
        }
    }
}
