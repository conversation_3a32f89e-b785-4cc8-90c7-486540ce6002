package com.projectbriefbuddy;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.projectbriefbuddy.dto.RegisterRequest;
import com.projectbriefbuddy.dto.AuthenticationRequest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureMockMvc
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:testdb",
    "spring.datasource.driver-class-name=org.h2.Driver",
    "spring.jpa.hibernate.ddl-auto=create-drop"
})
@Transactional
public class AuthenticationIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void testUserRegistrationAndLogin() throws Exception {
        // Test user registration
        RegisterRequest registerRequest = new RegisterRequest();
        registerRequest.setEmail("<EMAIL>");
        registerRequest.setPassword("demo123");
        registerRequest.setFullName("Demo User");

        mockMvc.perform(post("/api/auth/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(registerRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.access_token").exists())
                .andExpect(jsonPath("$.user.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.user.fullName").value("Demo User"));

        // Test user login
        AuthenticationRequest loginRequest = new AuthenticationRequest();
        loginRequest.setEmail("<EMAIL>");
        loginRequest.setPassword("demo123");

        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.access_token").exists())
                .andExpect(jsonPath("$.user.email").value("<EMAIL>"));
    }

    @Test
    public void testInvalidLogin() throws Exception {
        AuthenticationRequest loginRequest = new AuthenticationRequest();
        loginRequest.setEmail("<EMAIL>");
        loginRequest.setPassword("wrongpassword");

        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isUnauthorized());
    }

    @Test
    public void testDuplicateRegistration() throws Exception {
        // Register first user
        RegisterRequest registerRequest = new RegisterRequest();
        registerRequest.setEmail("<EMAIL>");
        registerRequest.setPassword("password123");
        registerRequest.setFullName("Test User");

        mockMvc.perform(post("/api/auth/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(registerRequest)))
                .andExpect(status().isOk());

        // Try to register same email again
        mockMvc.perform(post("/api/auth/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(registerRequest)))
                .andExpect(status().isBadRequest());
    }
}
