package com.projectbriefbuddy.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "requirements")
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(exclude = "project")
public class Requirement {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false)
    private String title;
    
    @Column(columnDefinition = "TEXT")
    private String description;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private RequirementType type;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private RequirementCategory category;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private Priority priority;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private RequirementStatus status;
    
    @ElementCollection
    @CollectionTable(name = "requirement_acceptance_criteria", 
                    joinColumns = @JoinColumn(name = "requirement_id"))
    @Column(name = "criteria")
    private List<String> acceptanceCriteria;
    
    @Column(name = "external_id")
    private String externalId;
    
    @Column(name = "ai_confidence_score")
    private Double aiConfidenceScore;
    
    @Column(name = "ai_extracted")
    private Boolean aiExtracted = false;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id", nullable = false)
    private Project project;
    
    @OneToMany(mappedBy = "requirement", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<RequirementValidation> validations;
    
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    public enum RequirementType {
        FUNCTIONAL,
        NON_FUNCTIONAL,
        TECHNICAL,
        BUSINESS,
        USER_STORY,
        CONSTRAINT,
        ASSUMPTION
    }
    
    public enum RequirementCategory {
        // Functional categories
        USER_INTERFACE,
        BUSINESS_LOGIC,
        DATA_MANAGEMENT,
        INTEGRATION,
        WORKFLOW,
        REPORTING,
        
        // Non-functional categories
        PERFORMANCE,
        SECURITY,
        SCALABILITY,
        USABILITY,
        RELIABILITY,
        AVAILABILITY,
        MAINTAINABILITY,
        COMPLIANCE,
        
        // Technical categories
        ARCHITECTURE,
        PLATFORM,
        INFRASTRUCTURE,
        API,
        DATABASE,
        DEPLOYMENT,
        
        // Business categories
        STAKEHOLDER,
        OBJECTIVE,
        SUCCESS_CRITERIA,
        RISK,
        CONSTRAINT,
        ASSUMPTION
    }
    
    public enum Priority {
        HIGH,
        MEDIUM,
        LOW
    }
    
    public enum RequirementStatus {
        DRAFT,
        REVIEW,
        APPROVED,
        IMPLEMENTED,
        TESTING,
        COMPLETED,
        REJECTED
    }
}