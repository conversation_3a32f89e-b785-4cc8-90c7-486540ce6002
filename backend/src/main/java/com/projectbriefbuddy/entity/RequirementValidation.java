package com.projectbriefbuddy.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Entity
@Table(name = "requirement_validations")
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(exclude = "requirement")
public class RequirementValidation {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private ValidationType type;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private ValidationStatus status;
    
    @Column(columnDefinition = "TEXT")
    private String description;
    
    @Column(columnDefinition = "TEXT")
    private String suggestion;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private Priority priority;
    
    @Column(name = "ai_generated")
    private Boolean aiGenerated = false;
    
    @Column(name = "confidence_score")
    private Double confidenceScore;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "requirement_id", nullable = false)
    private Requirement requirement;
    
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    public enum ValidationType {
        COMPLETENESS,
        CLARITY,
        CONSISTENCY,
        TESTABILITY,
        FEASIBILITY,
        CONFLICT,
        MISSING_DEPENDENCY,
        REDUNDANCY
    }
    
    public enum ValidationStatus {
        PENDING,
        RESOLVED,
        ACKNOWLEDGED,
        REJECTED
    }
    
    public enum Priority {
        HIGH,
        MEDIUM,
        LOW
    }
}