package com.projectbriefbuddy.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "ai_conversations")
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(exclude = "project")
public class AiConversation {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false)
    private String title;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private ConversationType type;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private ConversationStatus status;
    
    @Column(columnDefinition = "TEXT")
    private String context;
    
    @Column(name = "total_messages")
    private Integer totalMessages = 0;
    
    @Column(name = "completion_percentage")
    private Double completionPercentage = 0.0;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id", nullable = false)
    private Project project;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    @OneToMany(mappedBy = "conversation", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<AiConversationMessage> messages;
    
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    public enum ConversationType {
        REQUIREMENTS_GATHERING,
        REQUIREMENTS_REFINEMENT,
        GAP_ANALYSIS,
        STAKEHOLDER_INTERVIEW,
        TECHNICAL_CLARIFICATION,
        VALIDATION_REVIEW
    }
    
    public enum ConversationStatus {
        ACTIVE,
        PAUSED,
        COMPLETED,
        ARCHIVED
    }
}