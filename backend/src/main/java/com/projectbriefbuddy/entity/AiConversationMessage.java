package com.projectbriefbuddy.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Entity
@Table(name = "ai_conversation_messages")
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(exclude = "conversation")
public class AiConversationMessage {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private MessageRole role;
    
    @Column(columnDefinition = "TEXT", nullable = false)
    private String content;
    
    @Column(name = "message_order", nullable = false)
    private Integer messageOrder;
    
    @Column(name = "processing_time_ms")
    private Long processingTimeMs;
    
    @Column(name = "token_count")
    private Integer tokenCount;
    
    @Column(name = "confidence_score")
    private Double confidenceScore;
    
    @Column(columnDefinition = "TEXT")
    private String metadata;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "conversation_id", nullable = false)
    private AiConversation conversation;
    
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }
    
    public enum MessageRole {
        USER,
        ASSISTANT,
        SYSTEM
    }
}