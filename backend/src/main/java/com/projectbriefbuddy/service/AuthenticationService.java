package com.projectbriefbuddy.service;

import com.projectbriefbuddy.dto.*;
import com.projectbriefbuddy.entity.RefreshToken;
import com.projectbriefbuddy.entity.User;
import com.projectbriefbuddy.exception.BadRequestException;
import com.projectbriefbuddy.exception.UnauthorizedException;
import com.projectbriefbuddy.repository.RefreshTokenRepository;
import com.projectbriefbuddy.repository.UserRepository;
import com.projectbriefbuddy.security.JwtService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class AuthenticationService {
    
    private final UserRepository userRepository;
    private final RefreshTokenRepository refreshTokenRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtService jwtService;
    private final AuthenticationManager authenticationManager;
    
    @Value("${spring.security.jwt.expiration}")
    private long jwtExpiration;
    
    @Value("${spring.security.jwt.refresh-expiration}")
    private long refreshExpiration;
    
    @Transactional
    public AuthenticationResponse register(RegisterRequest request) {
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new BadRequestException("Email already registered");
        }
        
        var user = new User(
                request.getEmail(),
                passwordEncoder.encode(request.getPassword()),
                request.getFullName()
        );
        
        var savedUser = userRepository.save(user);
        var jwtToken = jwtService.generateToken(user);
        var refreshToken = createRefreshToken(savedUser);
        
        var response = new AuthenticationResponse();
        response.setAccessToken(jwtToken);
        response.setRefreshToken(refreshToken.getToken());
        response.setTokenType("Bearer");
        response.setExpiresIn(jwtExpiration / 1000);
        response.setUser(UserDto.fromEntity(savedUser));
        return response;
    }
    
    @Transactional
    public AuthenticationResponse authenticate(AuthenticationRequest request) {
        authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(
                        request.getEmail(),
                        request.getPassword()
                )
        );
        
        var user = userRepository.findByEmail(request.getEmail())
                .orElseThrow(() -> new UnauthorizedException("Invalid credentials"));
        
        var jwtToken = jwtService.generateToken(user);
        var refreshToken = createRefreshToken(user);
        
        var response = new AuthenticationResponse();
        response.setAccessToken(jwtToken);
        response.setRefreshToken(refreshToken.getToken());
        response.setTokenType("Bearer");
        response.setExpiresIn(jwtExpiration / 1000);
        response.setUser(UserDto.fromEntity(user));
        return response;
    }
    
    @Transactional
    public AuthenticationResponse refreshToken(String refreshTokenStr) {
        var refreshToken = refreshTokenRepository.findByToken(refreshTokenStr)
                .orElseThrow(() -> new UnauthorizedException("Invalid refresh token"));
        
        if (refreshToken.isExpired()) {
            refreshTokenRepository.delete(refreshToken);
            throw new UnauthorizedException("Refresh token expired");
        }
        
        var user = refreshToken.getUser();
        var jwtToken = jwtService.generateToken(user);
        
        var response = new AuthenticationResponse();
        response.setAccessToken(jwtToken);
        response.setRefreshToken(refreshTokenStr);
        response.setTokenType("Bearer");
        response.setExpiresIn(jwtExpiration / 1000);
        response.setUser(UserDto.fromEntity(user));
        return response;
    }
    
    @Transactional
    public void logout(String refreshTokenStr) {
        refreshTokenRepository.findByToken(refreshTokenStr)
                .ifPresent(refreshTokenRepository::delete);
    }
    
    private RefreshToken createRefreshToken(User user) {
        refreshTokenRepository.deleteByUser(user);
        
        var refreshToken = RefreshToken.builder()
                .token(UUID.randomUUID().toString())
                .user(user)
                .expiresAt(LocalDateTime.now().plusSeconds(refreshExpiration / 1000))
                .build();
        
        return refreshTokenRepository.save(refreshToken);
    }
}