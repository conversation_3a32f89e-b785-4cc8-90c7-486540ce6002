package com.projectbriefbuddy.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class NotificationService {
    
    private final SimpMessagingTemplate messagingTemplate;
    
    public void notifyProjectUpdate(UUID projectId, String updateType, Object data) {
        try {
            Map<String, Object> message = Map.of(
                    "type", updateType,
                    "projectId", projectId,
                    "data", data,
                    "timestamp", System.currentTimeMillis()
            );
            
            messagingTemplate.convertAndSend("/topic/project/" + projectId, message);
            log.debug("Sent project update notification: {} for project {}", updateType, projectId);
        } catch (Exception e) {
            log.error("Failed to send project update notification", e);
        }
    }
    
    public void notifyRecordingUpdate(UUID projectId, String updateType, Object data) {
        try {
            Map<String, Object> message = Map.of(
                    "type", updateType,
                    "projectId", projectId,
                    "data", data,
                    "timestamp", System.currentTimeMillis()
            );
            
            messagingTemplate.convertAndSend("/topic/project/" + projectId + "/recordings", message);
            log.debug("Sent recording update notification: {} for project {}", updateType, projectId);
        } catch (Exception e) {
            log.error("Failed to send recording update notification", e);
        }
    }
    
    public void notifyBriefUpdate(UUID projectId, String updateType, Object data) {
        try {
            Map<String, Object> message = Map.of(
                    "type", updateType,
                    "projectId", projectId,
                    "data", data,
                    "timestamp", System.currentTimeMillis()
            );
            
            messagingTemplate.convertAndSend("/topic/project/" + projectId + "/briefs", message);
            log.debug("Sent brief update notification: {} for project {}", updateType, projectId);
        } catch (Exception e) {
            log.error("Failed to send brief update notification", e);
        }
    }
    
    public void notifyUserUpdate(UUID userId, String updateType, Object data) {
        try {
            Map<String, Object> message = Map.of(
                    "type", updateType,
                    "data", data,
                    "timestamp", System.currentTimeMillis()
            );
            
            messagingTemplate.convertAndSendToUser(userId.toString(), "/queue/notifications", message);
            log.debug("Sent user notification: {} for user {}", updateType, userId);
        } catch (Exception e) {
            log.error("Failed to send user notification", e);
        }
    }
}