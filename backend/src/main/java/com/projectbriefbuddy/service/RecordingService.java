package com.projectbriefbuddy.service;

import com.projectbriefbuddy.dto.RecordingDto;
import com.projectbriefbuddy.entity.Project;
import com.projectbriefbuddy.entity.Recording;
import com.projectbriefbuddy.entity.User;
import com.projectbriefbuddy.exception.NotFoundException;
import com.projectbriefbuddy.repository.RecordingRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class RecordingService {
    
    private final RecordingRepository recordingRepository;
    private final ProjectService projectService;
    private final FileStorageService fileStorageService;
    
    @Transactional
    public RecordingDto createRecording(UUID projectId, String title, MultipartFile file, User currentUser) throws IOException {
        var project = projectService.getProjectEntity(projectId, currentUser);
        
        // Store the file
        var filePath = fileStorageService.storeFile(file, "recordings");
        
        var recording = Recording.builder()
                .project(project)
                .title(title)
                .filePath(filePath)
                .fileSize(file.getSize())
                .mimeType(file.getContentType())
                .status(Recording.RecordingStatus.PENDING)
                .build();
        
        var savedRecording = recordingRepository.save(recording);
        return RecordingDto.fromEntity(savedRecording);
    }
    
    @Transactional(readOnly = true)
    public List<RecordingDto> getRecordings(UUID projectId, User currentUser) {
        var project = projectService.getProjectEntity(projectId, currentUser);
        return recordingRepository.findByProjectOrderByCreatedAtDesc(project)
                .stream()
                .map(RecordingDto::fromEntity)
                .collect(Collectors.toList());
    }
    
    @Transactional(readOnly = true)
    public Page<RecordingDto> getRecordingsPaginated(UUID projectId, User currentUser, Pageable pageable) {
        var project = projectService.getProjectEntity(projectId, currentUser);
        return recordingRepository.findByProject(project, pageable)
                .map(RecordingDto::fromEntity);
    }
    
    @Transactional(readOnly = true)
    public RecordingDto getRecording(UUID projectId, UUID recordingId, User currentUser) {
        var project = projectService.getProjectEntity(projectId, currentUser);
        var recording = recordingRepository.findByIdAndProject(recordingId, project)
                .orElseThrow(() -> new NotFoundException("Recording not found"));
        
        return RecordingDto.fromEntity(recording);
    }
    
    @Transactional
    public RecordingDto updateRecording(UUID projectId, UUID recordingId, String title, String transcription, User currentUser) {
        var project = projectService.getProjectEntity(projectId, currentUser);
        var recording = recordingRepository.findByIdAndProject(recordingId, project)
                .orElseThrow(() -> new NotFoundException("Recording not found"));
        
        if (title != null) {
            recording.setTitle(title);
        }
        if (transcription != null) {
            recording.setTranscription(transcription);
            recording.setStatus(Recording.RecordingStatus.TRANSCRIBED);
        }
        
        var updatedRecording = recordingRepository.save(recording);
        return RecordingDto.fromEntity(updatedRecording);
    }
    
    @Transactional
    public void deleteRecording(UUID projectId, UUID recordingId, User currentUser) {
        var project = projectService.getProjectEntity(projectId, currentUser);
        var recording = recordingRepository.findByIdAndProject(recordingId, project)
                .orElseThrow(() -> new NotFoundException("Recording not found"));
        
        // Delete the file
        if (recording.getFilePath() != null) {
            fileStorageService.deleteFile(recording.getFilePath());
        }
        
        recordingRepository.delete(recording);
    }
}