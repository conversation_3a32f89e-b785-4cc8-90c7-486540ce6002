package com.projectbriefbuddy.service.ai;

import com.projectbriefbuddy.entity.AiConversation;
import com.projectbriefbuddy.entity.AiConversationMessage;
import com.projectbriefbuddy.entity.Project;
import com.projectbriefbuddy.entity.User;
import com.projectbriefbuddy.repository.AiConversationRepository;
import com.projectbriefbuddy.repository.ProjectRepository;
import com.projectbriefbuddy.repository.UserRepository;
import dev.langchain4j.memory.chat.MessageWindowChatMemory;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.input.Prompt;
import dev.langchain4j.model.input.PromptTemplate;
import dev.langchain4j.model.output.Response;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.data.message.SystemMessage;
import dev.langchain4j.data.message.UserMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class AiConversationService {

    private final ChatLanguageModel chatLanguageModel;
    private final AiConversationRepository conversationRepository;
    private final ProjectRepository projectRepository;
    private final UserRepository userRepository;
    
    private final Map<Long, MessageWindowChatMemory> conversationMemories = new HashMap<>();
    
    private static final String REQUIREMENTS_GATHERING_SYSTEM_PROMPT = """
        You are an expert business analyst conducting a requirements gathering session for a software project.
        
        Your goal is to help gather comprehensive project requirements by asking targeted questions and analyzing responses.
        
        Project Context: {{projectContext}}
        
        Guidelines:
        1. Ask specific, open-ended questions to understand business needs
        2. Probe for details about user roles, workflows, and pain points
        3. Identify functional and non-functional requirements
        4. Clarify technical constraints and preferences
        5. Understand success criteria and acceptance criteria
        6. Keep questions focused and avoid overwhelming the user
        7. Build upon previous responses to dig deeper
        8. Suggest requirements based on industry best practices when appropriate
        
        Always be professional, clear, and helpful. If you identify gaps or potential issues, bring them up constructively.
        """;
    
    private static final String REQUIREMENTS_REFINEMENT_SYSTEM_PROMPT = """
        You are an expert business analyst helping to refine and improve project requirements.
        
        Your role is to:
        1. Review existing requirements for completeness and clarity
        2. Identify missing requirements or gaps
        3. Suggest improvements to existing requirements
        4. Flag potential conflicts or inconsistencies
        5. Recommend best practices and industry standards
        6. Help prioritize requirements based on business value
        
        Current Requirements: {{currentRequirements}}
        
        Be constructive and specific in your feedback. Provide actionable suggestions.
        """;
    
    @Transactional
    public AiConversation startConversation(Long projectId, Long userId, AiConversation.ConversationType type, String initialContext) {
        log.info("Starting AI conversation for project {} with user {}", projectId, userId);
        
        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new RuntimeException("Project not found"));
        
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        
        // Create conversation
        AiConversation conversation = new AiConversation();
        conversation.setProject(project);
        conversation.setUser(user);
        conversation.setType(type);
        conversation.setStatus(AiConversation.ConversationStatus.ACTIVE);
        conversation.setTitle(generateConversationTitle(type, project.getName()));
        conversation.setContext(initialContext);
        conversation.setTotalMessages(0);
        conversation.setCompletionPercentage(0.0);
        
        conversation = conversationRepository.save(conversation);
        
        // Initialize chat memory
        MessageWindowChatMemory memory = MessageWindowChatMemory.withMaxMessages(20);
        conversationMemories.put(conversation.getId(), memory);
        
        // Add system message based on conversation type
        String systemPrompt = getSystemPrompt(type, project, initialContext);
        memory.add(SystemMessage.from(systemPrompt));
        
        // Generate initial AI message
        String initialMessage = generateInitialMessage(type, project, initialContext);
        addMessageToConversation(conversation, AiConversationMessage.MessageRole.ASSISTANT, initialMessage);
        
        log.info("AI conversation started with ID: {}", conversation.getId());
        return conversation;
    }
    
    @Transactional
    public String sendMessage(Long conversationId, String userMessage) {
        log.info("Processing message for conversation {}", conversationId);
        
        AiConversation conversation = conversationRepository.findById(conversationId)
                .orElseThrow(() -> new RuntimeException("Conversation not found"));
        
        if (conversation.getStatus() != AiConversation.ConversationStatus.ACTIVE) {
            throw new RuntimeException("Conversation is not active");
        }
        
        // Get or create memory for this conversation
        MessageWindowChatMemory memory = conversationMemories.computeIfAbsent(
                conversationId, 
                k -> loadConversationMemory(conversation)
        );
        
        // Add user message to memory and database
        memory.add(UserMessage.from(userMessage));
        addMessageToConversation(conversation, AiConversationMessage.MessageRole.USER, userMessage);
        
        // Generate AI response
        long startTime = System.currentTimeMillis();
        Response<AiMessage> response = chatLanguageModel.generate(memory.messages());
        long processingTime = System.currentTimeMillis() - startTime;
        
        String aiResponse = response.content().text();
        
        // Add AI response to memory and database
        memory.add(response.content());
        AiConversationMessage aiMessage = addMessageToConversation(conversation, AiConversationMessage.MessageRole.ASSISTANT, aiResponse);
        aiMessage.setProcessingTimeMs(processingTime);
        
        // Update conversation stats
        conversation.setTotalMessages(conversation.getTotalMessages() + 2);
        conversation.setCompletionPercentage(calculateCompletionPercentage(conversation));
        conversationRepository.save(conversation);
        
        log.info("AI response generated for conversation {}", conversationId);
        return aiResponse;
    }
    
    @Transactional
    public void pauseConversation(Long conversationId) {
        AiConversation conversation = conversationRepository.findById(conversationId)
                .orElseThrow(() -> new RuntimeException("Conversation not found"));
        
        conversation.setStatus(AiConversation.ConversationStatus.PAUSED);
        conversationRepository.save(conversation);
        
        log.info("Conversation {} paused", conversationId);
    }
    
    @Transactional
    public void resumeConversation(Long conversationId) {
        AiConversation conversation = conversationRepository.findById(conversationId)
                .orElseThrow(() -> new RuntimeException("Conversation not found"));
        
        conversation.setStatus(AiConversation.ConversationStatus.ACTIVE);
        conversationRepository.save(conversation);
        
        log.info("Conversation {} resumed", conversationId);
    }
    
    @Transactional
    public void completeConversation(Long conversationId) {
        AiConversation conversation = conversationRepository.findById(conversationId)
                .orElseThrow(() -> new RuntimeException("Conversation not found"));
        
        conversation.setStatus(AiConversation.ConversationStatus.COMPLETED);
        conversation.setCompletionPercentage(100.0);
        conversationRepository.save(conversation);
        
        // Clean up memory
        conversationMemories.remove(conversationId);
        
        log.info("Conversation {} completed", conversationId);
    }
    
    public List<AiConversation> getProjectConversations(Long projectId) {
        return conversationRepository.findByProjectIdOrderByCreatedAtDesc(projectId);
    }
    
    public List<AiConversationMessage> getConversationMessages(Long conversationId) {
        AiConversation conversation = conversationRepository.findById(conversationId)
                .orElseThrow(() -> new RuntimeException("Conversation not found"));
        
        return conversation.getMessages().stream()
                .sorted(Comparator.comparing(AiConversationMessage::getMessageOrder))
                .collect(Collectors.toList());
    }
    
    private String getSystemPrompt(AiConversation.ConversationType type, Project project, String context) {
        String projectContext = String.format("Project: %s\nDescription: %s\nAdditional Context: %s", 
                project.getName(), project.getDescription(), context);
        
        switch (type) {
            case REQUIREMENTS_GATHERING:
                return REQUIREMENTS_GATHERING_SYSTEM_PROMPT.replace("{{projectContext}}", projectContext);
            case REQUIREMENTS_REFINEMENT:
                return REQUIREMENTS_REFINEMENT_SYSTEM_PROMPT.replace("{{currentRequirements}}", context);
            default:
                return "You are a helpful AI assistant helping with project requirements.";
        }
    }
    
    private String generateInitialMessage(AiConversation.ConversationType type, Project project, String context) {
        switch (type) {
            case REQUIREMENTS_GATHERING:
                return String.format("Hello! I'm here to help gather comprehensive requirements for the '%s' project. " +
                        "To start, could you tell me about the main problem or business need this project is meant to solve?", 
                        project.getName());
            case REQUIREMENTS_REFINEMENT:
                return "I'll help you refine and improve your project requirements. " +
                        "I've reviewed your current requirements and I'm ready to discuss potential improvements, " +
                        "identify gaps, and suggest enhancements. Where would you like to start?";
            default:
                return "Hello! I'm here to help with your project requirements. How can I assist you today?";
        }
    }
    
    private AiConversationMessage addMessageToConversation(AiConversation conversation, AiConversationMessage.MessageRole role, String content) {
        AiConversationMessage message = new AiConversationMessage();
        message.setConversation(conversation);
        message.setRole(role);
        message.setContent(content);
        message.setMessageOrder(conversation.getMessages().size());
        
        conversation.getMessages().add(message);
        return message;
    }
    
    private MessageWindowChatMemory loadConversationMemory(AiConversation conversation) {
        MessageWindowChatMemory memory = MessageWindowChatMemory.withMaxMessages(20);
        
        // Load existing messages into memory
        List<AiConversationMessage> messages = conversation.getMessages().stream()
                .sorted(Comparator.comparing(AiConversationMessage::getMessageOrder))
                .collect(Collectors.toList());
        
        for (AiConversationMessage msg : messages) {
            ChatMessage chatMessage = switch (msg.getRole()) {
                case USER -> UserMessage.from(msg.getContent());
                case ASSISTANT -> AiMessage.from(msg.getContent());
                case SYSTEM -> SystemMessage.from(msg.getContent());
            };
            memory.add(chatMessage);
        }
        
        return memory;
    }
    
    private String generateConversationTitle(AiConversation.ConversationType type, String projectName) {
        return switch (type) {
            case REQUIREMENTS_GATHERING -> "Requirements Gathering - " + projectName;
            case REQUIREMENTS_REFINEMENT -> "Requirements Refinement - " + projectName;
            case GAP_ANALYSIS -> "Gap Analysis - " + projectName;
            case STAKEHOLDER_INTERVIEW -> "Stakeholder Interview - " + projectName;
            case TECHNICAL_CLARIFICATION -> "Technical Clarification - " + projectName;
            case VALIDATION_REVIEW -> "Validation Review - " + projectName;
        };
    }
    
    private Double calculateCompletionPercentage(AiConversation conversation) {
        // Simple heuristic based on message count and conversation type
        int messageCount = conversation.getTotalMessages();
        int targetMessages = switch (conversation.getType()) {
            case REQUIREMENTS_GATHERING -> 20;
            case REQUIREMENTS_REFINEMENT -> 15;
            case GAP_ANALYSIS -> 10;
            case STAKEHOLDER_INTERVIEW -> 25;
            case TECHNICAL_CLARIFICATION -> 12;
            case VALIDATION_REVIEW -> 8;
        };
        
        return Math.min(100.0, (double) messageCount / targetMessages * 100.0);
    }
}