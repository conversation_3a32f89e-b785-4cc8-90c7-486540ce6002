package com.projectbriefbuddy.service.ai;

import com.projectbriefbuddy.entity.Requirement;
import com.projectbriefbuddy.entity.RequirementValidation;
import com.projectbriefbuddy.repository.RequirementRepository;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.input.Prompt;
import dev.langchain4j.model.input.PromptTemplate;
import dev.langchain4j.model.output.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class GapAnalysisService {

    private final ChatLanguageModel chatLanguageModel;
    private final RequirementRepository requirementRepository;

    private static final String REQUIREMENTS_VALIDATION_PROMPT = """
        You are an expert business analyst reviewing project requirements for completeness, clarity, and quality.
        
        Requirements to validate: {{requirements}}
        
        Please analyze each requirement and identify issues in these categories:
        
        1. COMPLETENESS: Are all necessary details provided?
        2. CLARITY: Is the requirement clear and unambiguous?
        3. CONSISTENCY: Do requirements conflict with each other?
        4. TESTABILITY: Can the requirement be verified/tested?
        5. FEASIBILITY: Is the requirement realistic and achievable?
        6. REDUNDANCY: Are there duplicate or overlapping requirements?
        7. MISSING_DEPENDENCY: Are there unstated dependencies?
        
        For each requirement, provide validation results in JSON format:
        {
          "validationResults": [
            {
              "requirementId": "requirement identifier",
              "validationIssues": [
                {
                  "type": "COMPLETENESS|CLARITY|CONSISTENCY|TESTABILITY|FEASIBILITY|REDUNDANCY|MISSING_DEPENDENCY",
                  "severity": "HIGH|MEDIUM|LOW",
                  "description": "detailed description of the issue",
                  "suggestion": "specific suggestion to fix the issue",
                  "examples": ["example of improvement"]
                }
              ],
              "overallScore": score_0_to_10,
              "strengths": ["strength1", "strength2"],
              "improvementAreas": ["area1", "area2"]
            }
          ],
          "globalIssues": [
            {
              "type": "issue type",
              "description": "cross-requirement issue",
              "affectedRequirements": ["req1", "req2"],
              "recommendation": "how to address"
            }
          ],
          "summary": {
            "totalRequirements": number,
            "averageScore": average_score,
            "highSeverityIssues": count,
            "mediumSeverityIssues": count,
            "lowSeverityIssues": count,
            "recommendations": ["top recommendation 1", "top recommendation 2"]
          }
        }
        """;

    private static final String GAP_ANALYSIS_PROMPT = """
        Perform a comprehensive gap analysis for the following project requirements.
        
        Project Requirements: {{requirements}}
        Project Type: {{projectType}}
        Industry Standards: {{industryStandards}}
        
        Identify gaps in these areas:
        1. Functional Requirements
        2. Non-Functional Requirements
        3. Technical Requirements
        4. Security Requirements
        5. Compliance Requirements
        6. User Experience Requirements
        7. Integration Requirements
        8. Data Requirements
        9. Testing Requirements
        10. Deployment Requirements
        
        Return the gap analysis in JSON format:
        {
          "gapAnalysis": {
            "functionalGaps": [
              {
                "area": "gap area",
                "description": "what's missing",
                "impact": "HIGH|MEDIUM|LOW",
                "recommendation": "specific action",
                "examples": ["example requirement"]
              }
            ],
            "nonFunctionalGaps": [
              {
                "area": "performance|security|scalability|usability|reliability",
                "description": "gap description",
                "impact": "HIGH|MEDIUM|LOW",
                "recommendation": "how to address",
                "acceptanceCriteria": ["criteria1", "criteria2"]
              }
            ],
            "technicalGaps": [
              {
                "area": "technical area",
                "description": "gap description",
                "impact": "HIGH|MEDIUM|LOW",
                "recommendation": "technical solution",
                "considerations": ["consideration1", "consideration2"]
              }
            ],
            "processGaps": [
              {
                "area": "process area",
                "description": "gap description",
                "impact": "HIGH|MEDIUM|LOW",
                "recommendation": "process improvement"
              }
            ],
            "prioritizedActions": [
              {
                "action": "action description",
                "priority": "HIGH|MEDIUM|LOW",
                "effort": "LOW|MEDIUM|HIGH",
                "impact": "HIGH|MEDIUM|LOW",
                "timeline": "suggested timeline"
              }
            ],
            "riskAssessment": [
              {
                "risk": "risk description",
                "probability": "HIGH|MEDIUM|LOW",
                "impact": "HIGH|MEDIUM|LOW",
                "mitigation": "mitigation strategy"
              }
            ]
          }
        }
        """;

    private static final String STAKEHOLDER_ANALYSIS_PROMPT = """
        Analyze the stakeholder requirements and identify potential gaps or conflicts.
        
        Requirements: {{requirements}}
        Stakeholder Information: {{stakeholderInfo}}
        
        Analyze for:
        1. Stakeholder needs coverage
        2. Conflicting stakeholder interests
        3. Missing stakeholder perspectives
        4. Power/influence considerations
        5. Communication requirements
        
        Return analysis in JSON format:
        {
          "stakeholderAnalysis": {
            "identifiedStakeholders": [
              {
                "role": "stakeholder role",
                "influence": "HIGH|MEDIUM|LOW",
                "interest": "HIGH|MEDIUM|LOW",
                "requirements": ["req1", "req2"],
                "concerns": ["concern1", "concern2"]
              }
            ],
            "missingStakeholders": [
              {
                "role": "missing stakeholder",
                "importance": "HIGH|MEDIUM|LOW",
                "potentialImpact": "impact description",
                "engagementStrategy": "how to engage"
              }
            ],
            "conflicts": [
              {
                "stakeholders": ["stakeholder1", "stakeholder2"],
                "conflictArea": "area of conflict",
                "description": "conflict description",
                "resolutionStrategy": "how to resolve"
              }
            ],
            "engagementPlan": [
              {
                "stakeholder": "stakeholder role",
                "frequency": "communication frequency",
                "method": "communication method",
                "topics": ["topic1", "topic2"]
              }
            ]
          }
        }
        """;

    public String validateRequirements(Long projectId) {
        log.info("Validating requirements for project {}", projectId);
        
        List<Requirement> requirements = requirementRepository.findByProjectIdOrderByCreatedAtDesc(projectId);
        
        if (requirements.isEmpty()) {
            throw new RuntimeException("No requirements found for project");
        }
        
        String requirementsText = formatRequirementsForAnalysis(requirements);
        
        try {
            PromptTemplate template = PromptTemplate.from(REQUIREMENTS_VALIDATION_PROMPT);
            Prompt prompt = template.apply(Map.of("requirements", requirementsText));
            
            Response<String> response = chatLanguageModel.generate(prompt.toUserMessage());
            
            String validationResults = response.content();
            log.info("Requirements validation completed for project {}", projectId);
            
            return validationResults;
        } catch (Exception e) {
            log.error("Error validating requirements for project {}", projectId, e);
            throw new RuntimeException("Failed to validate requirements: " + e.getMessage());
        }
    }

    public String performGapAnalysis(Long projectId, String projectType, String industryStandards) {
        log.info("Performing gap analysis for project {}", projectId);
        
        List<Requirement> requirements = requirementRepository.findByProjectIdOrderByCreatedAtDesc(projectId);
        String requirementsText = formatRequirementsForAnalysis(requirements);
        
        try {
            PromptTemplate template = PromptTemplate.from(GAP_ANALYSIS_PROMPT);
            Prompt prompt = template.apply(Map.of(
                    "requirements", requirementsText,
                    "projectType", projectType,
                    "industryStandards", industryStandards
            ));
            
            Response<String> response = chatLanguageModel.generate(prompt.toUserMessage());
            
            String gapAnalysis = response.content();
            log.info("Gap analysis completed for project {}", projectId);
            
            return gapAnalysis;
        } catch (Exception e) {
            log.error("Error performing gap analysis for project {}", projectId, e);
            throw new RuntimeException("Failed to perform gap analysis: " + e.getMessage());
        }
    }

    public String analyzeStakeholderRequirements(Long projectId, String stakeholderInfo) {
        log.info("Analyzing stakeholder requirements for project {}", projectId);
        
        List<Requirement> requirements = requirementRepository.findByProjectIdOrderByCreatedAtDesc(projectId);
        String requirementsText = formatRequirementsForAnalysis(requirements);
        
        try {
            PromptTemplate template = PromptTemplate.from(STAKEHOLDER_ANALYSIS_PROMPT);
            Prompt prompt = template.apply(Map.of(
                    "requirements", requirementsText,
                    "stakeholderInfo", stakeholderInfo
            ));
            
            Response<String> response = chatLanguageModel.generate(prompt.toUserMessage());
            
            String stakeholderAnalysis = response.content();
            log.info("Stakeholder analysis completed for project {}", projectId);
            
            return stakeholderAnalysis;
        } catch (Exception e) {
            log.error("Error analyzing stakeholder requirements for project {}", projectId, e);
            throw new RuntimeException("Failed to analyze stakeholder requirements: " + e.getMessage());
        }
    }

    public String generateQualityMetrics(Long projectId) {
        log.info("Generating quality metrics for project {}", projectId);
        
        List<Requirement> requirements = requirementRepository.findByProjectIdOrderByCreatedAtDesc(projectId);
        
        if (requirements.isEmpty()) {
            return "{\"error\": \"No requirements found for quality analysis\"}";
        }
        
        // Calculate basic metrics
        long totalRequirements = requirements.size();
        long functionalRequirements = requirements.stream()
                .filter(r -> r.getType() == Requirement.RequirementType.FUNCTIONAL)
                .count();
        long nonFunctionalRequirements = requirements.stream()
                .filter(r -> r.getType() == Requirement.RequirementType.NON_FUNCTIONAL)
                .count();
        long highPriorityRequirements = requirements.stream()
                .filter(r -> r.getPriority() == Requirement.Priority.HIGH)
                .count();
        long requirementsWithAcceptanceCriteria = requirements.stream()
                .filter(r -> r.getAcceptanceCriteria() != null && !r.getAcceptanceCriteria().isEmpty())
                .count();
        long aiExtractedRequirements = requirements.stream()
                .filter(r -> Boolean.TRUE.equals(r.getAiExtracted()))
                .count();
        
        double avgConfidenceScore = requirements.stream()
                .filter(r -> r.getAiConfidenceScore() != null)
                .mapToDouble(Requirement::getAiConfidenceScore)
                .average()
                .orElse(0.0);
        
        // Format metrics as JSON
        String metrics = String.format("""
            {
              "qualityMetrics": {
                "totalRequirements": %d,
                "functionalRequirements": %d,
                "nonFunctionalRequirements": %d,
                "highPriorityRequirements": %d,
                "requirementsWithAcceptanceCriteria": %d,
                "acceptanceCriteriaCompleteness": %.2f,
                "aiExtractedRequirements": %d,
                "averageAiConfidence": %.2f,
                "distributionByType": {
                  "functional": %.2f,
                  "nonFunctional": %.2f,
                  "technical": %.2f,
                  "business": %.2f
                },
                "distributionByPriority": {
                  "high": %.2f,
                  "medium": %.2f,
                  "low": %.2f
                },
                "qualityIndicators": {
                  "completeness": %.2f,
                  "clarity": %.2f,
                  "consistency": %.2f,
                  "testability": %.2f
                }
              }
            }
            """,
            totalRequirements,
            functionalRequirements,
            nonFunctionalRequirements,
            highPriorityRequirements,
            requirementsWithAcceptanceCriteria,
            (double) requirementsWithAcceptanceCriteria / totalRequirements * 100,
            aiExtractedRequirements,
            avgConfidenceScore,
            (double) functionalRequirements / totalRequirements * 100,
            (double) nonFunctionalRequirements / totalRequirements * 100,
            (double) requirements.stream().filter(r -> r.getType() == Requirement.RequirementType.TECHNICAL).count() / totalRequirements * 100,
            (double) requirements.stream().filter(r -> r.getType() == Requirement.RequirementType.BUSINESS).count() / totalRequirements * 100,
            (double) highPriorityRequirements / totalRequirements * 100,
            (double) requirements.stream().filter(r -> r.getPriority() == Requirement.Priority.MEDIUM).count() / totalRequirements * 100,
            (double) requirements.stream().filter(r -> r.getPriority() == Requirement.Priority.LOW).count() / totalRequirements * 100,
            calculateCompletenessScore(requirements),
            calculateClarityScore(requirements),
            calculateConsistencyScore(requirements),
            calculateTestabilityScore(requirements)
        );
        
        log.info("Quality metrics generated for project {}", projectId);
        return metrics;
    }

    private String formatRequirementsForAnalysis(List<Requirement> requirements) {
        return requirements.stream()
                .map(req -> String.format(
                        "ID: %s\nTitle: %s\nType: %s\nCategory: %s\nPriority: %s\nDescription: %s\nAcceptance Criteria: %s\n",
                        req.getExternalId() != null ? req.getExternalId() : req.getId().toString(),
                        req.getTitle(),
                        req.getType(),
                        req.getCategory(),
                        req.getPriority(),
                        req.getDescription(),
                        req.getAcceptanceCriteria() != null ? String.join(", ", req.getAcceptanceCriteria()) : "None"
                ))
                .collect(Collectors.joining("\n---\n"));
    }

    private double calculateCompletenessScore(List<Requirement> requirements) {
        return requirements.stream()
                .mapToDouble(req -> {
                    double score = 0.0;
                    if (req.getTitle() != null && !req.getTitle().trim().isEmpty()) score += 25;
                    if (req.getDescription() != null && !req.getDescription().trim().isEmpty()) score += 25;
                    if (req.getAcceptanceCriteria() != null && !req.getAcceptanceCriteria().isEmpty()) score += 25;
                    if (req.getPriority() != null) score += 25;
                    return score;
                })
                .average()
                .orElse(0.0);
    }

    private double calculateClarityScore(List<Requirement> requirements) {
        return requirements.stream()
                .mapToDouble(req -> {
                    double score = 50.0; // Base score
                    if (req.getDescription() != null && req.getDescription().length() > 50) score += 25;
                    if (req.getAcceptanceCriteria() != null && req.getAcceptanceCriteria().size() > 1) score += 25;
                    return score;
                })
                .average()
                .orElse(0.0);
    }

    private double calculateConsistencyScore(List<Requirement> requirements) {
        // Simple heuristic - requirements with similar keywords should have similar priorities
        return 75.0; // Placeholder - would need more sophisticated analysis
    }

    private double calculateTestabilityScore(List<Requirement> requirements) {
        return requirements.stream()
                .mapToDouble(req -> {
                    if (req.getAcceptanceCriteria() != null && !req.getAcceptanceCriteria().isEmpty()) {
                        return req.getAcceptanceCriteria().size() >= 2 ? 100.0 : 50.0;
                    }
                    return 0.0;
                })
                .average()
                .orElse(0.0);
    }
}