package com.projectbriefbuddy.service.ai;

import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.input.Prompt;
import dev.langchain4j.model.input.PromptTemplate;
import dev.langchain4j.model.output.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class TemplateMatchingService {

    private final ChatLanguageModel chatLanguageModel;

    private static final String COMPREHENSIVE_PROJECT_TEMPLATE = """
        # Comprehensive Project Requirements Template
        
        ## 1. Project Overview
        - Project Name
        - Project Description
        - Business Context
        - Problem Statement
        - Project Objectives
        - Success Criteria
        - Key Stakeholders
        
        ## 2. Business Requirements
        - Business Goals
        - Value Proposition
        - ROI Expectations
        - Business Rules
        - Compliance Requirements
        - Regulatory Constraints
        
        ## 3. Functional Requirements
        - Core Features
        - User Management
        - Data Management
        - Reporting & Analytics
        - Integration Requirements
        - Workflow Processes
        - Business Logic
        
        ## 4. Non-Functional Requirements
        - Performance Requirements
        - Security Requirements
        - Scalability Requirements
        - Availability Requirements
        - Reliability Requirements
        - Usability Requirements
        - Maintainability Requirements
        - Compliance Requirements
        
        ## 5. Technical Requirements
        - Technology Stack
        - Architecture Requirements
        - Platform Requirements
        - Database Requirements
        - Infrastructure Requirements
        - Development Environment
        - Deployment Requirements
        
        ## 6. User Experience Requirements
        - User Personas
        - User Journey Maps
        - UI/UX Requirements
        - Accessibility Requirements
        - Mobile Responsiveness
        - Browser Compatibility
        
        ## 7. Integration Requirements
        - Third-party Integrations
        - API Requirements
        - Data Exchange Formats
        - Authentication/Authorization
        - External System Dependencies
        
        ## 8. Data Requirements
        - Data Sources
        - Data Models
        - Data Migration
        - Data Backup & Recovery
        - Data Retention Policies
        - Data Privacy Requirements
        
        ## 9. Security Requirements
        - Authentication Methods
        - Authorization Levels
        - Data Encryption
        - Security Protocols
        - Vulnerability Management
        - Incident Response
        
        ## 10. Testing Requirements
        - Testing Strategy
        - Test Environments
        - Performance Testing
        - Security Testing
        - User Acceptance Testing
        - Automated Testing
        
        ## 11. Deployment & Operations
        - Deployment Strategy
        - Environment Configuration
        - Monitoring Requirements
        - Logging Requirements
        - Backup Procedures
        - Disaster Recovery
        
        ## 12. Project Constraints
        - Budget Constraints
        - Timeline Constraints
        - Resource Constraints
        - Technical Constraints
        - Legal/Regulatory Constraints
        
        ## 13. Risks & Assumptions
        - Project Risks
        - Risk Mitigation Strategies
        - Project Assumptions
        - Dependencies
        
        ## 14. Acceptance Criteria
        - Definition of Done
        - Acceptance Testing Criteria
        - Sign-off Requirements
        - Deliverable Specifications
        
        ## 15. Communication & Documentation
        - Communication Plan
        - Documentation Requirements
        - Training Requirements
        - Change Management
        """;

    private static final String TEMPLATE_MATCHING_PROMPT = """
        Compare the following project requirements against a comprehensive project template and identify gaps.
        
        Current Project Requirements: {{currentRequirements}}
        
        Comprehensive Template: {{template}}
        
        Please analyze and provide:
        
        1. Coverage Analysis: What percentage of the template is covered by current requirements?
        2. Missing Sections: Which major template sections are completely missing?
        3. Incomplete Sections: Which sections are partially covered but need more detail?
        4. Strengths: What aspects are well-covered in the current requirements?
        5. Priority Gaps: Which missing elements are most critical for project success?
        6. Suggestions: Specific recommendations for filling the gaps
        
        Return the analysis in JSON format:
        {
          "coverageAnalysis": {
            "overallCoverage": percentage_as_number,
            "sectionCoverage": [
              {
                "section": "section name",
                "coverage": percentage_as_number,
                "status": "COMPLETE|PARTIAL|MISSING"
              }
            ]
          },
          "missingSections": [
            {
              "section": "section name",
              "importance": "HIGH|MEDIUM|LOW",
              "description": "why this section is important"
            }
          ],
          "incompleteSections": [
            {
              "section": "section name",
              "currentCoverage": percentage_as_number,
              "missingElements": ["element1", "element2"],
              "suggestions": ["suggestion1", "suggestion2"]
            }
          ],
          "strengths": [
            {
              "area": "well-covered area",
              "description": "what makes it strong"
            }
          ],
          "priorityGaps": [
            {
              "gap": "gap description",
              "impact": "HIGH|MEDIUM|LOW",
              "recommendation": "specific action to address"
            }
          ],
          "improvementSuggestions": [
            {
              "category": "category name",
              "suggestion": "specific suggestion",
              "priority": "HIGH|MEDIUM|LOW",
              "effort": "LOW|MEDIUM|HIGH"
            }
          ]
        }
        """;

    private static final String REQUIREMENTS_GENERATION_PROMPT = """
        Based on the project context and identified gaps, generate detailed requirements for the missing sections.
        
        Project Context: {{projectContext}}
        Missing Sections: {{missingSections}}
        Project Type: {{projectType}}
        
        For each missing section, generate specific, actionable requirements that are:
        1. Relevant to the project context
        2. Specific and measurable where possible
        3. Realistic and achievable
        4. Following industry best practices
        
        Return the generated requirements in JSON format:
        {
          "generatedRequirements": [
            {
              "section": "section name",
              "requirements": [
                {
                  "id": "unique identifier",
                  "title": "requirement title",
                  "description": "detailed description",
                  "priority": "HIGH|MEDIUM|LOW",
                  "category": "functional|non-functional|technical|business",
                  "acceptanceCriteria": ["criteria1", "criteria2"],
                  "rationale": "why this requirement is important"
                }
              ]
            }
          ]
        }
        """;

    public String analyzeTemplateGaps(String currentRequirements) {
        log.info("Analyzing template gaps for requirements");
        
        try {
            PromptTemplate template = PromptTemplate.from(TEMPLATE_MATCHING_PROMPT);
            Prompt prompt = template.apply(Map.of(
                    "currentRequirements", currentRequirements,
                    "template", COMPREHENSIVE_PROJECT_TEMPLATE
            ));
            
            Response<String> response = chatLanguageModel.generate(prompt.toUserMessage());
            
            String analysis = response.content();
            log.info("Template gap analysis completed successfully");
            
            return analysis;
        } catch (Exception e) {
            log.error("Error analyzing template gaps", e);
            throw new RuntimeException("Failed to analyze template gaps: " + e.getMessage());
        }
    }

    public String generateMissingRequirements(String projectContext, String missingSections, String projectType) {
        log.info("Generating requirements for missing sections");
        
        try {
            PromptTemplate template = PromptTemplate.from(REQUIREMENTS_GENERATION_PROMPT);
            Prompt prompt = template.apply(Map.of(
                    "projectContext", projectContext,
                    "missingSections", missingSections,
                    "projectType", projectType
            ));
            
            Response<String> response = chatLanguageModel.generate(prompt.toUserMessage());
            
            String generatedRequirements = response.content();
            log.info("Requirements generation completed successfully");
            
            return generatedRequirements;
        } catch (Exception e) {
            log.error("Error generating missing requirements", e);
            throw new RuntimeException("Failed to generate requirements: " + e.getMessage());
        }
    }

    public String suggestProjectType(String projectDescription) {
        String projectTypePrompt = """
            Based on the following project description, suggest the most appropriate project type and sub-categories.
            
            Project Description: {{description}}
            
            Consider these project types:
            - Web Application (E-commerce, CMS, Portal, SaaS, etc.)
            - Mobile Application (Native, Hybrid, Progressive Web App)
            - Desktop Application (Business, Utility, Creative, etc.)
            - Enterprise System (ERP, CRM, HR, Financial, etc.)
            - Data & Analytics (BI, Data Warehouse, ML/AI, etc.)
            - Integration Project (API, ETL, Middleware, etc.)
            - Infrastructure (Cloud Migration, DevOps, Security, etc.)
            - IoT & Embedded Systems
            - Blockchain & Cryptocurrency
            - Game Development
            
            Return the analysis in JSON format:
            {
              "primaryType": "main project type",
              "subCategories": ["sub-category1", "sub-category2"],
              "confidence": confidence_score_0_to_1,
              "reasoning": "explanation of the classification",
              "alternativeTypes": [
                {
                  "type": "alternative type",
                  "probability": probability_0_to_1,
                  "reasoning": "why this could also apply"
                }
              ],
              "recommendedTemplate": "specific template recommendations",
              "keyConsiderations": ["consideration1", "consideration2"]
            }
            """;
        
        try {
            PromptTemplate template = PromptTemplate.from(projectTypePrompt);
            Prompt prompt = template.apply(Map.of("description", projectDescription));
            
            Response<String> response = chatLanguageModel.generate(prompt.toUserMessage());
            return response.content();
        } catch (Exception e) {
            log.error("Error suggesting project type", e);
            throw new RuntimeException("Failed to suggest project type: " + e.getMessage());
        }
    }

    public String generateRequirementsChecklist(String projectType, String projectContext) {
        String checklistPrompt = """
            Generate a comprehensive requirements checklist for a {{projectType}} project.
            
            Project Context: {{projectContext}}
            
            Create a checklist that covers all essential requirements categories with specific items to verify.
            Each checklist item should be:
            1. Specific and actionable
            2. Verifiable (can be checked as complete or incomplete)
            3. Relevant to the project type
            4. Ordered by importance/dependency
            
            Return the checklist in JSON format:
            {
              "checklist": [
                {
                  "category": "category name",
                  "priority": "HIGH|MEDIUM|LOW",
                  "items": [
                    {
                      "id": "unique_id",
                      "title": "checklist item title",
                      "description": "detailed description",
                      "required": true_or_false,
                      "dependsOn": ["other_item_ids"],
                      "validationCriteria": "how to verify completion",
                      "examples": ["example1", "example2"]
                    }
                  ]
                }
              ],
              "estimatedCompletionTime": "time estimate",
              "criticalPath": ["item_id1", "item_id2"],
              "recommendations": ["recommendation1", "recommendation2"]
            }
            """;
        
        try {
            PromptTemplate template = PromptTemplate.from(checklistPrompt);
            Prompt prompt = template.apply(Map.of(
                    "projectType", projectType,
                    "projectContext", projectContext
            ));
            
            Response<String> response = chatLanguageModel.generate(prompt.toUserMessage());
            return response.content();
        } catch (Exception e) {
            log.error("Error generating requirements checklist", e);
            throw new RuntimeException("Failed to generate checklist: " + e.getMessage());
        }
    }
}