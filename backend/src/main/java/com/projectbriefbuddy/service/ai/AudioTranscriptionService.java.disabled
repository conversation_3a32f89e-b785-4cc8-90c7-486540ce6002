package com.projectbriefbuddy.service.ai;

import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.input.Prompt;
import dev.langchain4j.model.input.PromptTemplate;
import dev.langchain4j.model.output.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.Map;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class AudioTranscriptionService {

    private final ChatLanguageModel chatLanguageModel;
    
    @Value("${app.upload.audio-dir:uploads/audio}")
    private String audioUploadDir;
    
    private static final String TRANSCRIPTION_CLEANUP_PROMPT = """
        You are a professional transcription editor. Clean up and improve the following raw transcription:
        
        Raw Transcription: {{rawTranscription}}
        
        Please:
        1. Fix grammar and punctuation
        2. Remove filler words (um, uh, like, you know, etc.)
        3. Organize into proper paragraphs
        4. Identify and format speaker changes if multiple speakers
        5. Ensure proper capitalization
        6. Make the text more readable while preserving the original meaning
        
        Return the cleaned transcription in the following format:
        {
          "cleanedTranscription": "improved transcription text",
          "speakerCount": number_of_speakers,
          "duration": "estimated_duration_in_minutes",
          "keyTopics": ["topic1", "topic2", "topic3"],
          "summary": "brief summary of the transcription"
        }
        
        Respond only with valid JSON.
        """;
    
    private static final String MEETING_ANALYSIS_PROMPT = """
        Analyze the following meeting transcription and extract key information:
        
        Transcription: {{transcription}}
        
        Please extract:
        1. Meeting participants and their roles
        2. Main agenda items discussed
        3. Decisions made
        4. Action items assigned
        5. Next steps or follow-ups
        6. Key requirements or specifications mentioned
        7. Timeline or deadline information
        8. Budget or cost discussions
        9. Technical requirements or constraints
        10. Stakeholder concerns or feedback
        
        Return the analysis in JSON format:
        {
          "meetingAnalysis": {
            "participants": [
              {
                "name": "participant name",
                "role": "their role",
                "contributions": ["key points they made"]
              }
            ],
            "agendaItems": ["item1", "item2"],
            "decisions": [
              {
                "decision": "what was decided",
                "rationale": "why it was decided",
                "impact": "impact on project"
              }
            ],
            "actionItems": [
              {
                "task": "what needs to be done",
                "assignee": "who is responsible",
                "deadline": "when it's due",
                "priority": "HIGH|MEDIUM|LOW"
              }
            ],
            "requirements": [
              {
                "requirement": "requirement description",
                "category": "functional|non-functional|technical",
                "priority": "HIGH|MEDIUM|LOW"
              }
            ],
            "timeline": {
              "projectStart": "start date if mentioned",
              "projectEnd": "end date if mentioned",
              "milestones": [
                {
                  "milestone": "milestone name",
                  "date": "target date",
                  "deliverables": ["deliverable1", "deliverable2"]
                }
              ]
            },
            "budget": {
              "totalBudget": "budget amount if mentioned",
              "breakdown": [
                {
                  "category": "category name",
                  "amount": "amount",
                  "notes": "additional notes"
                }
              ]
            },
            "concerns": [
              {
                "concern": "concern description",
                "stakeholder": "who raised it",
                "mitigation": "proposed mitigation if any"
              }
            ],
            "nextSteps": ["step1", "step2"],
            "followUpMeeting": "next meeting date if scheduled"
          }
        }
        
        If any information is not available, use null or empty arrays.
        """;
    
    public String processAudioFile(MultipartFile file) throws IOException {
        log.info("Processing audio file: {} (size: {} bytes)", file.getOriginalFilename(), file.getSize());
        
        // Create upload directory if it doesn't exist
        Path uploadPath = Path.of(audioUploadDir);
        Files.createDirectories(uploadPath);
        
        // Generate unique filename
        String filename = UUID.randomUUID().toString() + "_" + file.getOriginalFilename();
        Path filePath = uploadPath.resolve(filename);
        
        // Save file
        Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);
        
        // For now, return a placeholder since we don't have actual audio transcription
        // In a real implementation, you would use services like:
        // - Google Cloud Speech-to-Text
        // - AWS Transcribe
        // - Azure Cognitive Services Speech
        // - OpenAI Whisper
        
        String mockTranscription = generateMockTranscription();
        
        log.info("Audio file processed successfully: {}", filename);
        return mockTranscription;
    }
    
    public String cleanupTranscription(String rawTranscription) {
        log.info("Cleaning up transcription of length: {}", rawTranscription.length());
        
        try {
            PromptTemplate template = PromptTemplate.from(TRANSCRIPTION_CLEANUP_PROMPT);
            Prompt prompt = template.apply(Map.of("rawTranscription", rawTranscription));
            
            Response<String> response = chatLanguageModel.generate(prompt.toUserMessage());
            
            String cleanedTranscription = response.content();
            log.info("Transcription cleaned successfully");
            
            return cleanedTranscription;
        } catch (Exception e) {
            log.error("Error cleaning transcription", e);
            throw new RuntimeException("Failed to clean transcription: " + e.getMessage());
        }
    }
    
    public String analyzeMeetingTranscription(String transcription) {
        log.info("Analyzing meeting transcription");
        
        try {
            PromptTemplate template = PromptTemplate.from(MEETING_ANALYSIS_PROMPT);
            Prompt prompt = template.apply(Map.of("transcription", transcription));
            
            Response<String> response = chatLanguageModel.generate(prompt.toUserMessage());
            
            String analysis = response.content();
            log.info("Meeting analysis completed successfully");
            
            return analysis;
        } catch (Exception e) {
            log.error("Error analyzing meeting transcription", e);
            throw new RuntimeException("Failed to analyze meeting: " + e.getMessage());
        }
    }
    
    private String generateMockTranscription() {
        return """
            Um, so welcome everyone to the, uh, project kickoff meeting. I'm John, the project manager, and we're here to discuss the new e-commerce platform project.
            
            Sarah, our business analyst, can you walk us through the initial requirements?
            
            Sure, John. So, um, the client wants to build a comprehensive e-commerce platform. They need, uh, user registration, product catalog, shopping cart functionality, and payment processing. They also mentioned they want to integrate with their existing inventory management system.
            
            That sounds good. What about the technical requirements, Mike?
            
            Well, they prefer a web-based solution, responsive design for mobile devices. They want it to handle at least 10,000 concurrent users and integrate with their Salesforce CRM. They also need real-time inventory updates and order tracking.
            
            Excellent. Timeline-wise, they want to launch in 6 months, with a budget of around $200,000. We should also consider security requirements given that we're handling payment information.
            
            Any concerns or questions from the team?
            
            I think we need to clarify the payment gateway requirements and whether they want multi-currency support.
            
            Good point. Let me add that to our follow-up items. Next meeting is scheduled for next Friday to review the detailed requirements document.
            """;
    }
}