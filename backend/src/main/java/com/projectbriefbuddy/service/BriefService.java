package com.projectbriefbuddy.service;

import com.projectbriefbuddy.dto.BriefDto;
import com.projectbriefbuddy.dto.CreateBriefRequest;
import com.projectbriefbuddy.entity.Brief;
import com.projectbriefbuddy.entity.BriefSection;
import com.projectbriefbuddy.entity.User;
import com.projectbriefbuddy.exception.NotFoundException;
import com.projectbriefbuddy.repository.BriefRepository;
import com.projectbriefbuddy.repository.BriefSectionRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class BriefService {
    
    private final BriefRepository briefRepository;
    private final BriefSectionRepository briefSectionRepository;
    private final ProjectService projectService;
    
    @Transactional
    public BriefDto createBrief(UUID projectId, CreateBriefRequest request, User currentUser) {
        var project = projectService.getProjectEntity(projectId, currentUser);
        
        // Get next version number
        var maxVersion = briefRepository.findMaxVersionByProject(project).orElse(0);
        
        var brief = Brief.builder()
                .project(project)
                .title(request.getTitle())
                .content(request.getContent())
                .version(maxVersion + 1)
                .status(Brief.BriefStatus.DRAFT)
                .isFinal(false)
                .build();
        
        var savedBrief = briefRepository.save(brief);
        
        // Create sections if provided
        if (request.getSections() != null && !request.getSections().isEmpty()) {
            var sections = new ArrayList<BriefSection>();
            for (int i = 0; i < request.getSections().size(); i++) {
                var sectionRequest = request.getSections().get(i);
                var section = BriefSection.builder()
                        .brief(savedBrief)
                        .title(sectionRequest.getTitle())
                        .content(sectionRequest.getContent())
                        .orderIndex(sectionRequest.getOrderIndex() != null ? sectionRequest.getOrderIndex() : i)
                        .build();
                sections.add(section);
            }
            briefSectionRepository.saveAll(sections);
            savedBrief.setSections(sections);
        }
        
        return BriefDto.fromEntity(savedBrief);
    }
    
    @Transactional(readOnly = true)
    public List<BriefDto> getBriefs(UUID projectId, User currentUser) {
        var project = projectService.getProjectEntity(projectId, currentUser);
        return briefRepository.findByProjectOrderByCreatedAtDesc(project)
                .stream()
                .map(BriefDto::fromEntity)
                .collect(Collectors.toList());
    }
    
    @Transactional(readOnly = true)
    public Page<BriefDto> getBriefsPaginated(UUID projectId, User currentUser, Pageable pageable) {
        var project = projectService.getProjectEntity(projectId, currentUser);
        return briefRepository.findByProject(project, pageable)
                .map(BriefDto::fromEntity);
    }
    
    @Transactional(readOnly = true)
    public BriefDto getBrief(UUID projectId, UUID briefId, User currentUser) {
        var project = projectService.getProjectEntity(projectId, currentUser);
        var brief = briefRepository.findByIdAndProject(briefId, project)
                .orElseThrow(() -> new NotFoundException("Brief not found"));
        
        return BriefDto.fromEntity(brief);
    }
    
    @Transactional
    public BriefDto updateBrief(UUID projectId, UUID briefId, CreateBriefRequest request, User currentUser) {
        var project = projectService.getProjectEntity(projectId, currentUser);
        var brief = briefRepository.findByIdAndProject(briefId, project)
                .orElseThrow(() -> new NotFoundException("Brief not found"));
        
        if (request.getTitle() != null) {
            brief.setTitle(request.getTitle());
        }
        if (request.getContent() != null) {
            brief.setContent(request.getContent());
        }
        
        // Update sections if provided
        if (request.getSections() != null) {
            // Delete existing sections
            briefSectionRepository.deleteByBrief(brief);
            
            // Create new sections
            var sections = new ArrayList<BriefSection>();
            for (int i = 0; i < request.getSections().size(); i++) {
                var sectionRequest = request.getSections().get(i);
                var section = BriefSection.builder()
                        .brief(brief)
                        .title(sectionRequest.getTitle())
                        .content(sectionRequest.getContent())
                        .orderIndex(sectionRequest.getOrderIndex() != null ? sectionRequest.getOrderIndex() : i)
                        .build();
                sections.add(section);
            }
            briefSectionRepository.saveAll(sections);
            brief.setSections(sections);
        }
        
        var updatedBrief = briefRepository.save(brief);
        return BriefDto.fromEntity(updatedBrief);
    }
    
    @Transactional
    public BriefDto finalizeBrief(UUID projectId, UUID briefId, User currentUser) {
        var project = projectService.getProjectEntity(projectId, currentUser);
        var brief = briefRepository.findByIdAndProject(briefId, project)
                .orElseThrow(() -> new NotFoundException("Brief not found"));
        
        brief.setIsFinal(true);
        brief.setStatus(Brief.BriefStatus.APPROVED);
        
        var updatedBrief = briefRepository.save(brief);
        return BriefDto.fromEntity(updatedBrief);
    }
    
    @Transactional
    public void deleteBrief(UUID projectId, UUID briefId, User currentUser) {
        var project = projectService.getProjectEntity(projectId, currentUser);
        var brief = briefRepository.findByIdAndProject(briefId, project)
                .orElseThrow(() -> new NotFoundException("Brief not found"));
        
        briefRepository.delete(brief);
    }
}