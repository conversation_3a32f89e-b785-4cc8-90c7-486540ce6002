package com.projectbriefbuddy.service;

import com.projectbriefbuddy.dto.CreateProjectRequest;
import com.projectbriefbuddy.dto.ProjectDto;
import com.projectbriefbuddy.dto.UpdateProjectRequest;
import com.projectbriefbuddy.entity.Project;
import com.projectbriefbuddy.entity.User;
import com.projectbriefbuddy.exception.NotFoundException;
import com.projectbriefbuddy.exception.UnauthorizedException;
import com.projectbriefbuddy.repository.ProjectRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

@Service
@RequiredArgsConstructor
public class ProjectService {
    
    private final ProjectRepository projectRepository;
    
    @Transactional
    public ProjectDto createProject(CreateProjectRequest request, User currentUser) {
        var project = Project.builder()
                .name(request.getName())
                .description(request.getDescription())
                .status(Project.ProjectStatus.PLANNING)
                .user(currentUser)
                .build();
        
        var savedProject = projectRepository.save(project);
        return ProjectDto.fromEntity(savedProject);
    }
    
    @Transactional(readOnly = true)
    public Page<ProjectDto> getProjects(User currentUser, Pageable pageable) {
        return projectRepository.findByUserOrCollaborator(currentUser, pageable)
                .map(ProjectDto::fromEntity);
    }
    
    @Transactional(readOnly = true)
    public ProjectDto getProject(UUID projectId, User currentUser) {
        var project = projectRepository.findByIdAndUserOrCollaborator(projectId, currentUser)
                .orElseThrow(() -> new NotFoundException("Project not found"));
        
        return ProjectDto.fromEntity(project);
    }
    
    @Transactional
    public ProjectDto updateProject(UUID projectId, UpdateProjectRequest request, User currentUser) {
        var project = projectRepository.findByIdAndUser(projectId, currentUser)
                .orElseThrow(() -> new NotFoundException("Project not found"));
        
        if (request.getName() != null) {
            project.setName(request.getName());
        }
        if (request.getDescription() != null) {
            project.setDescription(request.getDescription());
        }
        if (request.getStatus() != null) {
            project.setStatus(request.getStatus());
        }
        
        var updatedProject = projectRepository.save(project);
        return ProjectDto.fromEntity(updatedProject);
    }
    
    @Transactional
    public void deleteProject(UUID projectId, User currentUser) {
        var project = projectRepository.findByIdAndUser(projectId, currentUser)
                .orElseThrow(() -> new NotFoundException("Project not found"));
        
        projectRepository.delete(project);
    }
    
    @Transactional(readOnly = true)
    public Project getProjectEntity(UUID projectId, User currentUser) {
        return projectRepository.findByIdAndUserOrCollaborator(projectId, currentUser)
                .orElseThrow(() -> new NotFoundException("Project not found"));
    }
    
    @Transactional(readOnly = true)
    public Project getProjectEntityForOwner(UUID projectId, User currentUser) {
        return projectRepository.findByIdAndUser(projectId, currentUser)
                .orElseThrow(() -> new NotFoundException("Project not found"));
    }
}