package com.projectbriefbuddy.repository;

import com.projectbriefbuddy.entity.Requirement;
import com.projectbriefbuddy.entity.Requirement.RequirementCategory;
import com.projectbriefbuddy.entity.Requirement.RequirementStatus;
import com.projectbriefbuddy.entity.Requirement.RequirementType;
import com.projectbriefbuddy.entity.Requirement.Priority;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

@Repository
public interface RequirementRepository extends JpaRepository<Requirement, Long> {
    
    List<Requirement> findByProjectIdOrderByCreatedAtDesc(UUID projectId);

    List<Requirement> findByProjectIdAndType(UUID projectId, RequirementType type);

    List<Requirement> findByProjectIdAndCategory(UUID projectId, RequirementCategory category);

    List<Requirement> findByProjectIdAndStatus(UUID projectId, RequirementStatus status);

    List<Requirement> findByProjectIdAndPriority(UUID projectId, Priority priority);

    List<Requirement> findByProjectIdAndAiExtracted(UUID projectId, Boolean aiExtracted);
    
    @Query("SELECT r FROM Requirement r WHERE r.project.id = :projectId AND r.status IN :statuses")
    List<Requirement> findByProjectIdAndStatusIn(@Param("projectId") UUID projectId, @Param("statuses") List<RequirementStatus> statuses);

    @Query("SELECT r FROM Requirement r WHERE r.project.id = :projectId AND r.type = :type AND r.status = :status")
    List<Requirement> findByProjectIdAndTypeAndStatus(@Param("projectId") UUID projectId, @Param("type") RequirementType type, @Param("status") RequirementStatus status);

    @Query("SELECT COUNT(r) FROM Requirement r WHERE r.project.id = :projectId")
    Long countByProjectId(@Param("projectId") UUID projectId);

    @Query("SELECT COUNT(r) FROM Requirement r WHERE r.project.id = :projectId AND r.status = :status")
    Long countByProjectIdAndStatus(@Param("projectId") UUID projectId, @Param("status") RequirementStatus status);

    @Query("SELECT COUNT(r) FROM Requirement r WHERE r.project.id = :projectId AND r.type = :type")
    Long countByProjectIdAndType(@Param("projectId") UUID projectId, @Param("type") RequirementType type);

    @Query("SELECT r FROM Requirement r WHERE r.project.id = :projectId AND r.aiConfidenceScore < :threshold")
    List<Requirement> findLowConfidenceRequirements(@Param("projectId") UUID projectId, @Param("threshold") Double threshold);

    @Query("SELECT r FROM Requirement r WHERE r.project.id = :projectId AND SIZE(r.acceptanceCriteria) = 0")
    List<Requirement> findRequirementsWithoutAcceptanceCriteria(@Param("projectId") UUID projectId);
}