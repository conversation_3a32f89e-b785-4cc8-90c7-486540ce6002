package com.projectbriefbuddy.repository;

import com.projectbriefbuddy.entity.Requirement;
import com.projectbriefbuddy.entity.Requirement.RequirementCategory;
import com.projectbriefbuddy.entity.Requirement.RequirementStatus;
import com.projectbriefbuddy.entity.Requirement.RequirementType;
import com.projectbriefbuddy.entity.Requirement.Priority;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RequirementRepository extends JpaRepository<Requirement, Long> {
    
    List<Requirement> findByProjectIdOrderByCreatedAtDesc(Long projectId);
    
    List<Requirement> findByProjectIdAndType(Long projectId, RequirementType type);
    
    List<Requirement> findByProjectIdAndCategory(Long projectId, RequirementCategory category);
    
    List<Requirement> findByProjectIdAndStatus(Long projectId, RequirementStatus status);
    
    List<Requirement> findByProjectIdAndPriority(Long projectId, Priority priority);
    
    List<Requirement> findByProjectIdAndAiExtracted(Long projectId, Boolean aiExtracted);
    
    @Query("SELECT r FROM Requirement r WHERE r.project.id = :projectId AND r.status IN :statuses")
    List<Requirement> findByProjectIdAndStatusIn(@Param("projectId") Long projectId, @Param("statuses") List<RequirementStatus> statuses);
    
    @Query("SELECT r FROM Requirement r WHERE r.project.id = :projectId AND r.type = :type AND r.status = :status")
    List<Requirement> findByProjectIdAndTypeAndStatus(@Param("projectId") Long projectId, @Param("type") RequirementType type, @Param("status") RequirementStatus status);
    
    @Query("SELECT COUNT(r) FROM Requirement r WHERE r.project.id = :projectId")
    Long countByProjectId(@Param("projectId") Long projectId);
    
    @Query("SELECT COUNT(r) FROM Requirement r WHERE r.project.id = :projectId AND r.status = :status")
    Long countByProjectIdAndStatus(@Param("projectId") Long projectId, @Param("status") RequirementStatus status);
    
    @Query("SELECT COUNT(r) FROM Requirement r WHERE r.project.id = :projectId AND r.type = :type")
    Long countByProjectIdAndType(@Param("projectId") Long projectId, @Param("type") RequirementType type);
    
    @Query("SELECT r FROM Requirement r WHERE r.project.id = :projectId AND r.aiConfidenceScore < :threshold")
    List<Requirement> findLowConfidenceRequirements(@Param("projectId") Long projectId, @Param("threshold") Double threshold);
    
    @Query("SELECT r FROM Requirement r WHERE r.project.id = :projectId AND SIZE(r.acceptanceCriteria) = 0")
    List<Requirement> findRequirementsWithoutAcceptanceCriteria(@Param("projectId") Long projectId);
}