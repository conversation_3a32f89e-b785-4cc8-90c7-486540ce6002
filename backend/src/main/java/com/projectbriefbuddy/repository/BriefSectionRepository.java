package com.projectbriefbuddy.repository;

import com.projectbriefbuddy.entity.Brief;
import com.projectbriefbuddy.entity.BriefSection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

@Repository
public interface BriefSectionRepository extends JpaRepository<BriefSection, UUID> {
    
    List<BriefSection> findByBriefOrderByOrderIndexAsc(Brief brief);
    
    void deleteByBrief(Brief brief);
}