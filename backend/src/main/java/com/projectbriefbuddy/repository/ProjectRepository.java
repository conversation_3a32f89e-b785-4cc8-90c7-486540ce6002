package com.projectbriefbuddy.repository;

import com.projectbriefbuddy.entity.Project;
import com.projectbriefbuddy.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

@Repository
public interface ProjectRepository extends JpaRepository<Project, UUID> {
    
    Page<Project> findByUser(User user, Pageable pageable);
    
    @Query("SELECT p FROM Project p LEFT JOIN p.collaborators c WHERE p.user = :user OR c.user = :user")
    Page<Project> findByUserOrCollaborator(@Param("user") User user, Pageable pageable);
    
    Optional<Project> findByIdAndUser(UUID id, User user);
    
    @Query("SELECT p FROM Project p LEFT JOIN p.collaborators c WHERE p.id = :id AND (p.user = :user OR c.user = :user)")
    Optional<Project> findByIdAndUserOrCollaborator(@Param("id") UUID id, @Param("user") User user);
    
    long countByUser(User user);
}