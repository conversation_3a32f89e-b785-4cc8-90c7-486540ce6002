package com.projectbriefbuddy.repository;

import com.projectbriefbuddy.entity.Project;
import com.projectbriefbuddy.entity.ProjectCollaborator;
import com.projectbriefbuddy.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface ProjectCollaboratorRepository extends JpaRepository<ProjectCollaborator, UUID> {
    
    List<ProjectCollaborator> findByProject(Project project);
    
    List<ProjectCollaborator> findByUser(User user);
    
    Optional<ProjectCollaborator> findByProjectAndUser(Project project, User user);
    
    boolean existsByProjectAndUser(Project project, User user);
    
    void deleteByProjectAndUser(Project project, User user);
}