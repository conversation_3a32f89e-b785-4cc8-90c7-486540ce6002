package com.projectbriefbuddy.repository;

import com.projectbriefbuddy.entity.Project;
import com.projectbriefbuddy.entity.Recording;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface RecordingRepository extends JpaRepository<Recording, UUID> {
    
    List<Recording> findByProjectOrderByCreatedAtDesc(Project project);
    
    Page<Recording> findByProject(Project project, Pageable pageable);
    
    Optional<Recording> findByIdAndProject(UUID id, Project project);
    
    long countByProject(Project project);
    
    void deleteByIdAndProject(UUID id, Project project);
}