package com.projectbriefbuddy.repository;

import com.projectbriefbuddy.entity.AiConversation;
import com.projectbriefbuddy.entity.AiConversation.ConversationStatus;
import com.projectbriefbuddy.entity.AiConversation.ConversationType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AiConversationRepository extends JpaRepository<AiConversation, Long> {
    
    List<AiConversation> findByProjectIdOrderByCreatedAtDesc(Long projectId);
    
    List<AiConversation> findByProjectIdAndStatus(Long projectId, ConversationStatus status);
    
    List<AiConversation> findByProjectIdAndType(Long projectId, ConversationType type);
    
    List<AiConversation> findByUserIdOrderByCreatedAtDesc(Long userId);
    
    @Query("SELECT c FROM AiConversation c WHERE c.project.id = :projectId AND c.status = :status ORDER BY c.updatedAt DESC")
    List<AiConversation> findActiveConversations(@Param("projectId") Long projectId, @Param("status") ConversationStatus status);
    
    @Query("SELECT c FROM AiConversation c WHERE c.project.id = :projectId AND c.type = :type AND c.status = :status ORDER BY c.updatedAt DESC")
    Optional<AiConversation> findLatestConversationByTypeAndStatus(@Param("projectId") Long projectId, @Param("type") ConversationType type, @Param("status") ConversationStatus status);
    
    @Query("SELECT COUNT(c) FROM AiConversation c WHERE c.project.id = :projectId AND c.status = :status")
    Long countByProjectIdAndStatus(@Param("projectId") Long projectId, @Param("status") ConversationStatus status);
}