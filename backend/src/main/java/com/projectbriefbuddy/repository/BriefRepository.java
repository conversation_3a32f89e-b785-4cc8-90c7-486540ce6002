package com.projectbriefbuddy.repository;

import com.projectbriefbuddy.entity.Brief;
import com.projectbriefbuddy.entity.Project;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface BriefRepository extends JpaRepository<Brief, UUID> {
    
    List<Brief> findByProjectOrderByCreatedAtDesc(Project project);
    
    Page<Brief> findByProject(Project project, Pageable pageable);
    
    Optional<Brief> findByIdAndProject(UUID id, Project project);
    
    @Query("SELECT b FROM Brief b WHERE b.project = :project AND b.isFinal = true ORDER BY b.createdAt DESC")
    Optional<Brief> findLatestFinalByProject(@Param("project") Project project);
    
    @Query("SELECT MAX(b.version) FROM Brief b WHERE b.project = :project")
    Optional<Integer> findMaxVersionByProject(@Param("project") Project project);
    
    long countByProject(Project project);
}