package com.projectbriefbuddy.dto;

import com.projectbriefbuddy.entity.Brief;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BriefDto {
    
    private UUID id;
    private UUID projectId;
    private String title;
    private String content;
    private Integer version;
    private String status;
    private boolean isFinal;
    private List<BriefSectionDto> sections;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    public static BriefDto fromEntity(Brief brief) {
        return BriefDto.builder()
                .id(brief.getId())
                .projectId(brief.getProject().getId())
                .title(brief.getTitle())
                .content(brief.getContent())
                .version(brief.getVersion())
                .status(brief.getStatus().name())
                .isFinal(brief.isFinal())
                .sections(brief.getSections() != null ? 
                    brief.getSections().stream()
                        .map(BriefSectionDto::fromEntity)
                        .collect(Collectors.toList()) : null)
                .createdAt(brief.getCreatedAt())
                .updatedAt(brief.getUpdatedAt())
                .build();
    }
}