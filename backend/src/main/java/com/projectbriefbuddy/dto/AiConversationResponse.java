package com.projectbriefbuddy.dto;

import com.projectbriefbuddy.entity.AiConversation;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AiConversationResponse {
    
    private Long id;
    private String title;
    private AiConversation.ConversationType type;
    private AiConversation.ConversationStatus status;
    private Long projectId;
    private Integer totalMessages;
    private Double completionPercentage;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}