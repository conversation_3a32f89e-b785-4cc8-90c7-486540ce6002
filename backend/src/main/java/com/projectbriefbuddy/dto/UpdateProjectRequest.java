package com.projectbriefbuddy.dto;

import com.projectbriefbuddy.entity.Project;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UpdateProjectRequest {
    
    private String name;
    private String description;
    
    @NotNull(message = "Status is required")
    private Project.ProjectStatus status;
}