package com.projectbriefbuddy.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreateBriefRequest {
    
    @NotBlank(message = "Brief title is required")
    private String title;
    
    private String content;
    private List<CreateBriefSectionRequest> sections;
    
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CreateBriefSectionRequest {
        @NotBlank(message = "Section title is required")
        private String title;
        private String content;
        private Integer orderIndex;
    }
}