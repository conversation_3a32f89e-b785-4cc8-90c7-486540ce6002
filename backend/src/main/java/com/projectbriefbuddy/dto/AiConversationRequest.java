package com.projectbriefbuddy.dto;

import com.projectbriefbuddy.entity.AiConversation;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class AiConversationRequest {
    
    @NotNull(message = "Project ID is required")
    private Long projectId;
    
    @NotNull(message = "Conversation type is required")
    private AiConversation.ConversationType type;
    
    private String initialContext;
}