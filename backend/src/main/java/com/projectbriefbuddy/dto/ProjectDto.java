package com.projectbriefbuddy.dto;

import com.projectbriefbuddy.entity.Project;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProjectDto {
    
    private UUID id;
    private String name;
    private String description;
    private String status;
    private UserDto owner;
    private int recordingsCount;
    private int briefsCount;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    public static ProjectDto fromEntity(Project project) {
        return ProjectDto.builder()
                .id(project.getId())
                .name(project.getName())
                .description(project.getDescription())
                .status(project.getStatus().name())
                .owner(UserDto.fromEntity(project.getUser()))
                .recordingsCount(project.getRecordings() != null ? project.getRecordings().size() : 0)
                .briefsCount(project.getBriefs() != null ? project.getBriefs().size() : 0)
                .createdAt(project.getCreatedAt())
                .updatedAt(project.getUpdatedAt())
                .build();
    }
}