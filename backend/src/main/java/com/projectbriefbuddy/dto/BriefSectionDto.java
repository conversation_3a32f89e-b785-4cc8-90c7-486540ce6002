package com.projectbriefbuddy.dto;

import com.projectbriefbuddy.entity.BriefSection;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BriefSectionDto {
    
    private UUID id;
    private String title;
    private String content;
    private Integer orderIndex;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    public static BriefSectionDto fromEntity(BriefSection section) {
        return BriefSectionDto.builder()
                .id(section.getId())
                .title(section.getTitle())
                .content(section.getContent())
                .orderIndex(section.getOrderIndex())
                .createdAt(section.getCreatedAt())
                .updatedAt(section.getUpdatedAt())
                .build();
    }
}