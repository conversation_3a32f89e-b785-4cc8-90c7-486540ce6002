package com.projectbriefbuddy.dto;

import com.projectbriefbuddy.entity.Recording;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RecordingDto {
    
    private UUID id;
    private UUID projectId;
    private String title;
    private String filePath;
    private Long fileSize;
    private Integer duration;
    private String mimeType;
    private String transcription;
    private String status;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    public static RecordingDto fromEntity(Recording recording) {
        return RecordingDto.builder()
                .id(recording.getId())
                .projectId(recording.getProject().getId())
                .title(recording.getTitle())
                .filePath(recording.getFilePath())
                .fileSize(recording.getFileSize())
                .duration(recording.getDuration())
                .mimeType(recording.getMimeType())
                .transcription(recording.getTranscription())
                .status(recording.getStatus().name())
                .createdAt(recording.getCreatedAt())
                .updatedAt(recording.getUpdatedAt())
                .build();
    }
}