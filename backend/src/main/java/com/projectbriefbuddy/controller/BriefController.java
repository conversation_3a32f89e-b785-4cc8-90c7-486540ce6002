package com.projectbriefbuddy.controller;

import com.projectbriefbuddy.dto.BriefDto;
import com.projectbriefbuddy.dto.CreateBriefRequest;
import com.projectbriefbuddy.entity.User;
import com.projectbriefbuddy.service.BriefService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/api/projects/{projectId}/briefs")
@RequiredArgsConstructor
public class BriefController {
    
    private final BriefService briefService;
    
    @PostMapping
    public ResponseEntity<BriefDto> createBrief(
            @PathVariable UUID projectId,
            @Valid @RequestBody CreateBriefRequest request,
            @AuthenticationPrincipal User currentUser
    ) {
        var brief = briefService.createBrief(projectId, request, currentUser);
        return ResponseEntity.status(HttpStatus.CREATED).body(brief);
    }
    
    @GetMapping
    public ResponseEntity<List<BriefDto>> getBriefs(
            @PathVariable UUID projectId,
            @AuthenticationPrincipal User currentUser
    ) {
        var briefs = briefService.getBriefs(projectId, currentUser);
        return ResponseEntity.ok(briefs);
    }
    
    @GetMapping("/paginated")
    public ResponseEntity<Page<BriefDto>> getBriefsPaginated(
            @PathVariable UUID projectId,
            @AuthenticationPrincipal User currentUser,
            @PageableDefault(size = 20) Pageable pageable
    ) {
        var briefs = briefService.getBriefsPaginated(projectId, currentUser, pageable);
        return ResponseEntity.ok(briefs);
    }
    
    @GetMapping("/{briefId}")
    public ResponseEntity<BriefDto> getBrief(
            @PathVariable UUID projectId,
            @PathVariable UUID briefId,
            @AuthenticationPrincipal User currentUser
    ) {
        var brief = briefService.getBrief(projectId, briefId, currentUser);
        return ResponseEntity.ok(brief);
    }
    
    @PutMapping("/{briefId}")
    public ResponseEntity<BriefDto> updateBrief(
            @PathVariable UUID projectId,
            @PathVariable UUID briefId,
            @Valid @RequestBody CreateBriefRequest request,
            @AuthenticationPrincipal User currentUser
    ) {
        var brief = briefService.updateBrief(projectId, briefId, request, currentUser);
        return ResponseEntity.ok(brief);
    }
    
    @PostMapping("/{briefId}/finalize")
    public ResponseEntity<BriefDto> finalizeBrief(
            @PathVariable UUID projectId,
            @PathVariable UUID briefId,
            @AuthenticationPrincipal User currentUser
    ) {
        var brief = briefService.finalizeBrief(projectId, briefId, currentUser);
        return ResponseEntity.ok(brief);
    }
    
    @DeleteMapping("/{briefId}")
    public ResponseEntity<Void> deleteBrief(
            @PathVariable UUID projectId,
            @PathVariable UUID briefId,
            @AuthenticationPrincipal User currentUser
    ) {
        briefService.deleteBrief(projectId, briefId, currentUser);
        return ResponseEntity.noContent().build();
    }
}