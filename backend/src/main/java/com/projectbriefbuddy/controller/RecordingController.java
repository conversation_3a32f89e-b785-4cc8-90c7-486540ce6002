package com.projectbriefbuddy.controller;

import com.projectbriefbuddy.dto.RecordingDto;
import com.projectbriefbuddy.entity.User;
import com.projectbriefbuddy.service.FileStorageService;
import com.projectbriefbuddy.service.RecordingService;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping("/api/projects/{projectId}/recordings")
@RequiredArgsConstructor
public class RecordingController {
    
    private final RecordingService recordingService;
    private final FileStorageService fileStorageService;
    
    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<RecordingDto> createRecording(
            @PathVariable UUID projectId,
            @RequestParam("title") String title,
            @RequestParam("file") MultipartFile file,
            @AuthenticationPrincipal User currentUser
    ) throws IOException {
        var recording = recordingService.createRecording(projectId, title, file, currentUser);
        return ResponseEntity.status(HttpStatus.CREATED).body(recording);
    }
    
    @GetMapping
    public ResponseEntity<List<RecordingDto>> getRecordings(
            @PathVariable UUID projectId,
            @AuthenticationPrincipal User currentUser
    ) {
        var recordings = recordingService.getRecordings(projectId, currentUser);
        return ResponseEntity.ok(recordings);
    }
    
    @GetMapping("/paginated")
    public ResponseEntity<Page<RecordingDto>> getRecordingsPaginated(
            @PathVariable UUID projectId,
            @AuthenticationPrincipal User currentUser,
            @PageableDefault(size = 20) Pageable pageable
    ) {
        var recordings = recordingService.getRecordingsPaginated(projectId, currentUser, pageable);
        return ResponseEntity.ok(recordings);
    }
    
    @GetMapping("/{recordingId}")
    public ResponseEntity<RecordingDto> getRecording(
            @PathVariable UUID projectId,
            @PathVariable UUID recordingId,
            @AuthenticationPrincipal User currentUser
    ) {
        var recording = recordingService.getRecording(projectId, recordingId, currentUser);
        return ResponseEntity.ok(recording);
    }
    
    @PutMapping("/{recordingId}")
    public ResponseEntity<RecordingDto> updateRecording(
            @PathVariable UUID projectId,
            @PathVariable UUID recordingId,
            @RequestBody Map<String, String> updates,
            @AuthenticationPrincipal User currentUser
    ) {
        var recording = recordingService.updateRecording(
                projectId, 
                recordingId, 
                updates.get("title"), 
                updates.get("transcription"), 
                currentUser
        );
        return ResponseEntity.ok(recording);
    }
    
    @DeleteMapping("/{recordingId}")
    public ResponseEntity<Void> deleteRecording(
            @PathVariable UUID projectId,
            @PathVariable UUID recordingId,
            @AuthenticationPrincipal User currentUser
    ) {
        recordingService.deleteRecording(projectId, recordingId, currentUser);
        return ResponseEntity.noContent().build();
    }
    
    @GetMapping("/{recordingId}/download")
    public ResponseEntity<Resource> downloadRecording(
            @PathVariable UUID projectId,
            @PathVariable UUID recordingId,
            @AuthenticationPrincipal User currentUser
    ) {
        var recording = recordingService.getRecording(projectId, recordingId, currentUser);
        var resource = fileStorageService.loadFileAsResource(recording.getFilePath());
        
        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(recording.getMimeType()))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + recording.getTitle() + "\"")
                .body(resource);
    }
}