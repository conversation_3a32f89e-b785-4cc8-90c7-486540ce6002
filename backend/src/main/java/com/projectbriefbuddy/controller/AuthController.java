package com.projectbriefbuddy.controller;

import com.projectbriefbuddy.dto.AuthenticationRequest;
import com.projectbriefbuddy.dto.AuthenticationResponse;
import com.projectbriefbuddy.dto.RegisterRequest;
import com.projectbriefbuddy.service.AuthenticationService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
public class AuthController {
    
    private final AuthenticationService authenticationService;
    
    @PostMapping("/register")
    public ResponseEntity<AuthenticationResponse> register(
            @Valid @RequestBody RegisterRequest request
    ) {
        return ResponseEntity.ok(authenticationService.register(request));
    }
    
    @PostMapping("/login")
    public ResponseEntity<AuthenticationResponse> authenticate(
            @Valid @RequestBody AuthenticationRequest request
    ) {
        return ResponseEntity.ok(authenticationService.authenticate(request));
    }
    
    @PostMapping("/refresh")
    public ResponseEntity<AuthenticationResponse> refreshToken(
            @RequestBody Map<String, String> request
    ) {
        String refreshToken = request.get("refresh_token");
        return ResponseEntity.ok(authenticationService.refreshToken(refreshToken));
    }
    
    @PostMapping("/logout")
    public ResponseEntity<Void> logout(
            @RequestBody Map<String, String> request
    ) {
        String refreshToken = request.get("refresh_token");
        authenticationService.logout(refreshToken);
        return ResponseEntity.noContent().build();
    }
}