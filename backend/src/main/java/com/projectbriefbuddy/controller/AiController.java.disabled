package com.projectbriefbuddy.controller;

import com.projectbriefbuddy.dto.AiConversationRequest;
import com.projectbriefbuddy.dto.AiConversationResponse;
import com.projectbriefbuddy.dto.ApiResponse;
import com.projectbriefbuddy.entity.AiConversation;
import com.projectbriefbuddy.entity.AiConversationMessage;
import com.projectbriefbuddy.service.ai.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/ai")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "AI Services", description = "AI-powered requirements analysis and generation")
public class AiController {

    private final RequirementsExtractionService requirementsExtractionService;
    private final AudioTranscriptionService audioTranscriptionService;
    private final AiConversationService aiConversationService;
    private final TemplateMatchingService templateMatchingService;
    private final GapAnalysisService gapAnalysisService;

    @PostMapping("/extract-requirements")
    @Operation(summary = "Extract requirements from text", description = "Use AI to extract structured requirements from unstructured text")
    public ResponseEntity<ApiResponse<String>> extractRequirements(@RequestBody String text) {
        try {
            String extractedRequirements = requirementsExtractionService.extractRequirementsFromText(text);
            return ResponseEntity.ok(ApiResponse.success(extractedRequirements));
        } catch (Exception e) {
            log.error("Error extracting requirements", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to extract requirements: " + e.getMessage()));
        }
    }

    @PostMapping("/refine-requirements")
    @Operation(summary = "Refine existing requirements", description = "Use AI to improve and refine existing requirements")
    public ResponseEntity<ApiResponse<String>> refineRequirements(@RequestBody String currentRequirements) {
        try {
            String refinedRequirements = requirementsExtractionService.refineRequirements(currentRequirements);
            return ResponseEntity.ok(ApiResponse.success(refinedRequirements));
        } catch (Exception e) {
            log.error("Error refining requirements", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to refine requirements: " + e.getMessage()));
        }
    }

    @PostMapping("/transcribe-audio")
    @Operation(summary = "Transcribe audio file", description = "Transcribe audio file and extract meeting information")
    public ResponseEntity<ApiResponse<String>> transcribeAudio(
            @Parameter(description = "Audio file to transcribe") @RequestParam("file") MultipartFile file) {
        try {
            String transcription = audioTranscriptionService.processAudioFile(file);
            String cleanedTranscription = audioTranscriptionService.cleanupTranscription(transcription);
            return ResponseEntity.ok(ApiResponse.success(cleanedTranscription));
        } catch (Exception e) {
            log.error("Error transcribing audio", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to transcribe audio: " + e.getMessage()));
        }
    }

    @PostMapping("/analyze-meeting")
    @Operation(summary = "Analyze meeting transcription", description = "Extract key information from meeting transcription")
    public ResponseEntity<ApiResponse<String>> analyzeMeeting(@RequestBody String transcription) {
        try {
            String analysis = audioTranscriptionService.analyzeMeetingTranscription(transcription);
            return ResponseEntity.ok(ApiResponse.success(analysis));
        } catch (Exception e) {
            log.error("Error analyzing meeting", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to analyze meeting: " + e.getMessage()));
        }
    }

    @PostMapping("/conversations")
    @Operation(summary = "Start AI conversation", description = "Start a new AI conversation for requirements gathering")
    public ResponseEntity<ApiResponse<AiConversationResponse>> startConversation(
            @Valid @RequestBody AiConversationRequest request,
            Authentication authentication) {
        try {
            Long userId = getUserIdFromAuth(authentication);
            AiConversation conversation = aiConversationService.startConversation(
                    request.getProjectId(),
                    userId,
                    request.getType(),
                    request.getInitialContext()
            );
            
            AiConversationResponse response = mapToResponse(conversation);
            return ResponseEntity.ok(ApiResponse.success(response));
        } catch (Exception e) {
            log.error("Error starting AI conversation", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to start conversation: " + e.getMessage()));
        }
    }

    @PostMapping("/conversations/{conversationId}/messages")
    @Operation(summary = "Send message to AI", description = "Send a message to an active AI conversation")
    public ResponseEntity<ApiResponse<String>> sendMessage(
            @PathVariable Long conversationId,
            @RequestBody String message) {
        try {
            String aiResponse = aiConversationService.sendMessage(conversationId, message);
            return ResponseEntity.ok(ApiResponse.success(aiResponse));
        } catch (Exception e) {
            log.error("Error sending message to AI", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to send message: " + e.getMessage()));
        }
    }

    @GetMapping("/conversations/{conversationId}/messages")
    @Operation(summary = "Get conversation messages", description = "Retrieve all messages from a conversation")
    public ResponseEntity<ApiResponse<List<AiConversationMessage>>> getConversationMessages(
            @PathVariable Long conversationId) {
        try {
            List<AiConversationMessage> messages = aiConversationService.getConversationMessages(conversationId);
            return ResponseEntity.ok(ApiResponse.success(messages));
        } catch (Exception e) {
            log.error("Error getting conversation messages", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to get messages: " + e.getMessage()));
        }
    }

    @PutMapping("/conversations/{conversationId}/pause")
    @Operation(summary = "Pause conversation", description = "Pause an active AI conversation")
    public ResponseEntity<ApiResponse<Void>> pauseConversation(@PathVariable Long conversationId) {
        try {
            aiConversationService.pauseConversation(conversationId);
            return ResponseEntity.ok(ApiResponse.success(null));
        } catch (Exception e) {
            log.error("Error pausing conversation", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to pause conversation: " + e.getMessage()));
        }
    }

    @PutMapping("/conversations/{conversationId}/resume")
    @Operation(summary = "Resume conversation", description = "Resume a paused AI conversation")
    public ResponseEntity<ApiResponse<Void>> resumeConversation(@PathVariable Long conversationId) {
        try {
            aiConversationService.resumeConversation(conversationId);
            return ResponseEntity.ok(ApiResponse.success(null));
        } catch (Exception e) {
            log.error("Error resuming conversation", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to resume conversation: " + e.getMessage()));
        }
    }

    @PutMapping("/conversations/{conversationId}/complete")
    @Operation(summary = "Complete conversation", description = "Mark an AI conversation as completed")
    public ResponseEntity<ApiResponse<Void>> completeConversation(@PathVariable Long conversationId) {
        try {
            aiConversationService.completeConversation(conversationId);
            return ResponseEntity.ok(ApiResponse.success(null));
        } catch (Exception e) {
            log.error("Error completing conversation", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to complete conversation: " + e.getMessage()));
        }
    }

    @GetMapping("/projects/{projectId}/conversations")
    @Operation(summary = "Get project conversations", description = "Get all AI conversations for a project")
    public ResponseEntity<ApiResponse<List<AiConversation>>> getProjectConversations(@PathVariable Long projectId) {
        try {
            List<AiConversation> conversations = aiConversationService.getProjectConversations(projectId);
            return ResponseEntity.ok(ApiResponse.success(conversations));
        } catch (Exception e) {
            log.error("Error getting project conversations", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to get conversations: " + e.getMessage()));
        }
    }

    @PostMapping("/analyze-template-gaps")
    @Operation(summary = "Analyze template gaps", description = "Analyze requirements against comprehensive template")
    public ResponseEntity<ApiResponse<String>> analyzeTemplateGaps(@RequestBody String currentRequirements) {
        try {
            String analysis = templateMatchingService.analyzeTemplateGaps(currentRequirements);
            return ResponseEntity.ok(ApiResponse.success(analysis));
        } catch (Exception e) {
            log.error("Error analyzing template gaps", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to analyze template gaps: " + e.getMessage()));
        }
    }

    @PostMapping("/suggest-project-type")
    @Operation(summary = "Suggest project type", description = "Suggest project type based on description")
    public ResponseEntity<ApiResponse<String>> suggestProjectType(@RequestBody String projectDescription) {
        try {
            String suggestion = templateMatchingService.suggestProjectType(projectDescription);
            return ResponseEntity.ok(ApiResponse.success(suggestion));
        } catch (Exception e) {
            log.error("Error suggesting project type", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to suggest project type: " + e.getMessage()));
        }
    }

    @PostMapping("/generate-checklist")
    @Operation(summary = "Generate requirements checklist", description = "Generate comprehensive requirements checklist")
    public ResponseEntity<ApiResponse<String>> generateChecklist(
            @RequestParam String projectType,
            @RequestBody String projectContext) {
        try {
            String checklist = templateMatchingService.generateRequirementsChecklist(projectType, projectContext);
            return ResponseEntity.ok(ApiResponse.success(checklist));
        } catch (Exception e) {
            log.error("Error generating checklist", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to generate checklist: " + e.getMessage()));
        }
    }

    @PostMapping("/projects/{projectId}/validate-requirements")
    @Operation(summary = "Validate project requirements", description = "Validate requirements for completeness and quality")
    public ResponseEntity<ApiResponse<String>> validateRequirements(@PathVariable Long projectId) {
        try {
            String validation = gapAnalysisService.validateRequirements(projectId);
            return ResponseEntity.ok(ApiResponse.success(validation));
        } catch (Exception e) {
            log.error("Error validating requirements", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to validate requirements: " + e.getMessage()));
        }
    }

    @PostMapping("/projects/{projectId}/gap-analysis")
    @Operation(summary = "Perform gap analysis", description = "Perform comprehensive gap analysis for project requirements")
    public ResponseEntity<ApiResponse<String>> performGapAnalysis(
            @PathVariable Long projectId,
            @RequestParam String projectType,
            @RequestParam(required = false) String industryStandards) {
        try {
            String analysis = gapAnalysisService.performGapAnalysis(projectId, projectType, industryStandards);
            return ResponseEntity.ok(ApiResponse.success(analysis));
        } catch (Exception e) {
            log.error("Error performing gap analysis", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to perform gap analysis: " + e.getMessage()));
        }
    }

    @GetMapping("/projects/{projectId}/quality-metrics")
    @Operation(summary = "Get quality metrics", description = "Get quality metrics for project requirements")
    public ResponseEntity<ApiResponse<String>> getQualityMetrics(@PathVariable Long projectId) {
        try {
            String metrics = gapAnalysisService.generateQualityMetrics(projectId);
            return ResponseEntity.ok(ApiResponse.success(metrics));
        } catch (Exception e) {
            log.error("Error getting quality metrics", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to get quality metrics: " + e.getMessage()));
        }
    }

    @PostMapping("/generate-questions")
    @Operation(summary = "Generate clarifying questions", description = "Generate questions to gather missing information")
    public ResponseEntity<ApiResponse<String>> generateQuestions(@RequestBody String partialRequirements) {
        try {
            String questions = requirementsExtractionService.generateQuestionsForMissingInfo(partialRequirements);
            return ResponseEntity.ok(ApiResponse.success(questions));
        } catch (Exception e) {
            log.error("Error generating questions", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to generate questions: " + e.getMessage()));
        }
    }

    private Long getUserIdFromAuth(Authentication authentication) {
        // Extract user ID from authentication
        // This depends on your authentication setup
        return 1L; // Placeholder
    }

    private AiConversationResponse mapToResponse(AiConversation conversation) {
        AiConversationResponse response = new AiConversationResponse();
        response.setId(conversation.getId());
        response.setTitle(conversation.getTitle());
        response.setType(conversation.getType());
        response.setStatus(conversation.getStatus());
        response.setProjectId(conversation.getProject().getId());
        response.setTotalMessages(conversation.getTotalMessages());
        response.setCompletionPercentage(conversation.getCompletionPercentage());
        response.setCreatedAt(conversation.getCreatedAt());
        response.setUpdatedAt(conversation.getUpdatedAt());
        return response;
    }
}