package com.projectbriefbuddy.controller;

import com.projectbriefbuddy.dto.CreateProjectRequest;
import com.projectbriefbuddy.dto.ProjectDto;
import com.projectbriefbuddy.dto.UpdateProjectRequest;
import com.projectbriefbuddy.entity.User;
import com.projectbriefbuddy.service.ProjectService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@RestController
@RequestMapping("/api/projects")
@RequiredArgsConstructor
public class ProjectController {
    
    private final ProjectService projectService;
    
    @PostMapping
    public ResponseEntity<ProjectDto> createProject(
            @Valid @RequestBody CreateProjectRequest request,
            @AuthenticationPrincipal User currentUser
    ) {
        var project = projectService.createProject(request, currentUser);
        return ResponseEntity.status(HttpStatus.CREATED).body(project);
    }
    
    @GetMapping
    public ResponseEntity<Page<ProjectDto>> getProjects(
            @AuthenticationPrincipal User currentUser,
            @PageableDefault(size = 20) Pageable pageable
    ) {
        var projects = projectService.getProjects(currentUser, pageable);
        return ResponseEntity.ok(projects);
    }
    
    @GetMapping("/{projectId}")
    public ResponseEntity<ProjectDto> getProject(
            @PathVariable UUID projectId,
            @AuthenticationPrincipal User currentUser
    ) {
        var project = projectService.getProject(projectId, currentUser);
        return ResponseEntity.ok(project);
    }
    
    @PutMapping("/{projectId}")
    public ResponseEntity<ProjectDto> updateProject(
            @PathVariable UUID projectId,
            @Valid @RequestBody UpdateProjectRequest request,
            @AuthenticationPrincipal User currentUser
    ) {
        var project = projectService.updateProject(projectId, request, currentUser);
        return ResponseEntity.ok(project);
    }
    
    @DeleteMapping("/{projectId}")
    public ResponseEntity<Void> deleteProject(
            @PathVariable UUID projectId,
            @AuthenticationPrincipal User currentUser
    ) {
        projectService.deleteProject(projectId, currentUser);
        return ResponseEntity.noContent().build();
    }
}