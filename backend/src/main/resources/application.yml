spring:
  application:
    name: project-brief-buddy-backend
  
  datasource:
    url: ****************************************************
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:postgres}
    driver-class-name: org.postgresql.Driver
  
  jpa:
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
    show-sql: false
  
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      
  security:
    jwt:
      secret: ${JWT_SECRET:404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970}
      expiration: 86400000 # 24 hours
      refresh-expiration: 604800000 # 7 days

server:
  port: 8080
  error:
    include-message: always
    include-binding-errors: always

logging:
  level:
    com.projectbriefbuddy: DEBUG
    org.springframework.security: DEBUG
    
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    
cors:
  allowed-origins:
    - http://localhost:5173
    - http://localhost:3000
  allowed-methods:
    - GET
    - POST
    - PUT
    - DELETE
    - OPTIONS
  allowed-headers:
    - "*"
  allow-credentials: true
  
file:
  upload-dir: ${FILE_UPLOAD_DIR:./uploads}

# AI Configuration
ai:
  gemini:
    api-key: ${GEMINI_API_KEY:}
    model: ${GEMINI_MODEL:gemini-pro}
  
app:
  upload:
    audio-dir: ${AUDIO_UPLOAD_DIR:uploads/audio}