/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/dto/UpdateProjectRequest.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/dto/BriefDto.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/ProjectBriefBuddyApplication.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/entity/Brief.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/entity/BriefSection.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/service/FileStorageService.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/security/JwtAuthenticationFilter.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/repository/RequirementRepository.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/dto/UserDto.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/exception/GlobalExceptionHandler.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/entity/User.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/entity/Requirement.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/dto/RegisterRequest.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/service/BriefService.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/repository/AiConversationRepository.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/controller/ProjectController.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/service/RecordingService.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/controller/RecordingController.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/repository/BriefSectionRepository.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/repository/RecordingRepository.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/config/SecurityConfig.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/dto/AuthenticationResponse.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/dto/BriefSectionDto.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/security/JwtService.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/dto/ApiResponse.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/dto/ProjectDto.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/config/OpenApiConfig.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/service/ProjectService.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/dto/AiConversationResponse.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/entity/Recording.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/entity/RequirementValidation.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/exception/BadRequestException.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/dto/RecordingDto.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/dto/AiConversationRequest.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/config/WebSocketConfig.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/repository/UserRepository.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/repository/BriefRepository.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/exception/NotFoundException.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/dto/AuthenticationRequest.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/entity/ProjectCollaborator.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/service/NotificationService.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/entity/AiConversationMessage.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/dto/CreateBriefRequest.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/dto/CreateProjectRequest.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/controller/BriefController.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/repository/ProjectCollaboratorRepository.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/exception/ErrorResponse.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/entity/AiConversation.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/controller/AuthController.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/service/UserDetailsServiceImpl.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/entity/RefreshToken.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/repository/RefreshTokenRepository.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/repository/ProjectRepository.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/exception/UnauthorizedException.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/entity/Project.java
/Users/<USER>/workspaces/g/project-brief-buddy/backend/src/main/java/com/projectbriefbuddy/service/AuthenticationService.java
