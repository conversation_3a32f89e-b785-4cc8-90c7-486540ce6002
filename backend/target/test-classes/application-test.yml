server:
  port: 0  # Random port for testing

spring:
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    driver-class-name: org.h2.Driver
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
    show-sql: false
  sql:
    init:
      mode: never
  
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

# JWT Configuration
jwt:
  secret: test-secret-key-for-integration-tests-that-is-long-enough
  expiration: 86400000  # 24 hours

# File storage
file:
  upload-dir: ./test-uploads

logging:
  level:
    com.projectbriefbuddy: DEBUG
    org.springframework.security: DEBUG
