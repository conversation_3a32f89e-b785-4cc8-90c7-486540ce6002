<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.projectbriefbuddy.integration.ProjectBriefBuddyIntegrationTest" time="2.824" tests="3" errors="1" skipped="0" failures="1">
  <properties>
    <property name="java.specification.version" value="24"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/Users/<USER>/workspaces/g/project-brief-buddy/backend/target/test-classes:/Users/<USER>/workspaces/g/project-brief-buddy/backend/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.2.0/spring-boot-starter-web-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.2.0/spring-boot-starter-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.2.0/spring-boot-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.2.0/spring-boot-autoconfigure-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.2.0/spring-boot-starter-logging-3.2.0.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.4.11/logback-classic-1.4.11.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.4.11/logback-core-1.4.11.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.21.1/log4j-to-slf4j-2.21.1.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.21.1/log4j-api-2.21.1.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.9/jul-to-slf4j-2.0.9.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.2/snakeyaml-2.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.2.0/spring-boot-starter-json-3.2.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.15.3/jackson-datatype-jdk8-2.15.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.15.3/jackson-module-parameter-names-2.15.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/3.2.0/spring-boot-starter-tomcat-3.2.0.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.16/tomcat-embed-core-10.1.16.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.16/tomcat-embed-websocket-10.1.16.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.1.1/spring-web-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.1.1/spring-beans-6.1.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.12.0/micrometer-observation-1.12.0.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.12.0/micrometer-commons-1.12.0.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.1.1/spring-webmvc-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.1.1/spring-context-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.1.1/spring-expression-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jpa/3.2.0/spring-boot-starter-data-jpa-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/3.2.0/spring-boot-starter-aop-3.2.0.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.20.1/aspectjweaver-1.9.20.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.2.0/spring-boot-starter-jdbc-3.2.0.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/5.0.1/HikariCP-5.0.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/6.1.1/spring-jdbc-6.1.1.jar:/Users/<USER>/.m2/repository/org/hibernate/orm/hibernate-core/6.3.1.Final/hibernate-core-6.3.1.Final.jar:/Users/<USER>/.m2/repository/jakarta/persistence/jakarta.persistence-api/3.1.0/jakarta.persistence-api-3.1.0.jar:/Users/<USER>/.m2/repository/jakarta/transaction/jakarta.transaction-api/2.0.1/jakarta.transaction-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.5.3.Final/jboss-logging-3.5.3.Final.jar:/Users/<USER>/.m2/repository/org/hibernate/common/hibernate-commons-annotations/6.0.6.Final/hibernate-commons-annotations-6.0.6.Final.jar:/Users/<USER>/.m2/repository/io/smallrye/jandex/3.1.2/jandex-3.1.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.6.0/classmate-1.6.0.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.14.10/byte-buddy-1.14.10.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/4.0.4/jaxb-runtime-4.0.4.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-core/4.0.4/jaxb-core-4.0.4.jar:/Users/<USER>/.m2/repository/org/eclipse/angus/angus-activation/2.0.1/angus-activation-2.0.1.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/txw2/4.0.4/txw2-4.0.4.jar:/Users/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/4.1.2/istack-commons-runtime-4.1.2.jar:/Users/<USER>/.m2/repository/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.10.1/antlr4-runtime-4.10.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jpa/3.2.0/spring-data-jpa-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.2.0/spring-data-commons-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/spring-orm/6.1.1/spring-orm-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.1.1/spring-tx-6.1.1.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.9/slf4j-api-2.0.9.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aspects/6.1.1/spring-aspects-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/3.2.0/spring-boot-starter-security-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.1.1/spring-aop-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/6.2.0/spring-security-config-6.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/6.2.0/spring-security-web-6.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.2.0/spring-boot-starter-validation-3.2.0.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.16/tomcat-embed-el-10.1.16.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.1.Final/hibernate-validator-8.0.1.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/3.2.0/spring-boot-starter-websocket-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/6.1.1/spring-messaging-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/6.1.1/spring-websocket-6.1.1.jar:/Users/<USER>/.m2/repository/org/postgresql/postgresql/42.6.0/postgresql-42.6.0.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.31.0/checker-qual-3.31.0.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.11.5/jjwt-api-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.11.5/jjwt-impl-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.11.5/jjwt-jackson-0.11.5.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.15.1/commons-io-2.15.1.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-webmvc-ui/2.3.0/springdoc-openapi-starter-webmvc-ui-2.3.0.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-webmvc-api/2.3.0/springdoc-openapi-starter-webmvc-api-2.3.0.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-common/2.3.0/springdoc-openapi-starter-common-2.3.0.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-core-jakarta/2.2.19/swagger-core-jakarta-2.2.19.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.13.0/commons-lang3-3.13.0.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations-jakarta/2.2.19/swagger-annotations-jakarta-2.2.19.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-models-jakarta/2.2.19/swagger-models-jakarta-2.2.19.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.15.3/jackson-dataformat-yaml-2.15.3.jar:/Users/<USER>/.m2/repository/org/webjars/swagger-ui/5.10.3/swagger-ui-5.10.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.15.3/jackson-databind-2.15.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.15.3/jackson-annotations-2.15.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.15.3/jackson-core-2.15.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.15.3/jackson-datatype-jsr310-2.15.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.2.0/spring-boot-starter-test-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.2.0/spring-boot-test-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.2.0/spring-boot-test-autoconfigure-3.2.0.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.8.0/json-path-2.8.0.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.1/jakarta.xml.bind-api-4.0.1.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.2/jakarta.activation-api-2.1.2.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.5.0/json-smart-2.5.0.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.0/accessors-smart-2.5.0.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/9.3/asm-9.3.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.24.2/assertj-core-3.24.2.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.0/awaitility-4.2.0.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.10.1/junit-jupiter-5.10.1.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.10.1/junit-jupiter-api-5.10.1.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.10.1/junit-platform-commons-1.10.1.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.10.1/junit-jupiter-params-5.10.1.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.10.1/junit-jupiter-engine-5.10.1.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.10.1/junit-platform-engine-1.10.1.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/5.7.0/mockito-core-5.7.0.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.14.10/byte-buddy-agent-1.14.10.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.7.0/mockito-junit-jupiter-5.7.0.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.1/jsonassert-1.5.1.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.1.1/spring-core-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.1.1/spring-jcl-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/6.1.1/spring-test-6.1.1.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.9.1/xmlunit-core-2.9.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-test/6.2.0/spring-security-test-6.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/6.2.0/spring-security-core-6.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.2.0/spring-security-crypto-6.2.0.jar:/Users/<USER>/.m2/repository/com/h2database/h2/2.2.224/h2-2.2.224.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.30/lombok-1.18.30.jar:"/>
    <property name="java.vm.vendor" value="Homebrew"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="catalina.useNaming" value="false"/>
    <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues"/>
    <property name="user.timezone" value="Asia/Kolkata"/>
    <property name="org.jboss.logging.provider" value="slf4j"/>
    <property name="os.name" value="Mac OS X"/>
    <property name="java.vm.specification.version" value="24"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="IN"/>
    <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/24.0.1/libexec/openjdk.jdk/Contents/Home/lib"/>
    <property name="sun.java.command" value="/Users/<USER>/workspaces/g/project-brief-buddy/backend/target/surefire/surefirebooter-20250714153952348_3.jar /Users/<USER>/workspaces/g/project-brief-buddy/backend/target/surefire 2025-07-14T15-39-52_320-jvmRun1 surefire-20250714153952348_1tmp surefire_0-20250714153952348_2tmp"/>
    <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="jdk.debug" value="release"/>
    <property name="test" value="ProjectBriefBuddyIntegrationTest"/>
    <property name="surefire.test.class.path" value="/Users/<USER>/workspaces/g/project-brief-buddy/backend/target/test-classes:/Users/<USER>/workspaces/g/project-brief-buddy/backend/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.2.0/spring-boot-starter-web-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.2.0/spring-boot-starter-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.2.0/spring-boot-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.2.0/spring-boot-autoconfigure-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.2.0/spring-boot-starter-logging-3.2.0.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.4.11/logback-classic-1.4.11.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.4.11/logback-core-1.4.11.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.21.1/log4j-to-slf4j-2.21.1.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.21.1/log4j-api-2.21.1.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.9/jul-to-slf4j-2.0.9.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.2/snakeyaml-2.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.2.0/spring-boot-starter-json-3.2.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.15.3/jackson-datatype-jdk8-2.15.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.15.3/jackson-module-parameter-names-2.15.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/3.2.0/spring-boot-starter-tomcat-3.2.0.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.16/tomcat-embed-core-10.1.16.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.16/tomcat-embed-websocket-10.1.16.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.1.1/spring-web-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.1.1/spring-beans-6.1.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.12.0/micrometer-observation-1.12.0.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.12.0/micrometer-commons-1.12.0.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.1.1/spring-webmvc-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.1.1/spring-context-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.1.1/spring-expression-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jpa/3.2.0/spring-boot-starter-data-jpa-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/3.2.0/spring-boot-starter-aop-3.2.0.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.20.1/aspectjweaver-1.9.20.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.2.0/spring-boot-starter-jdbc-3.2.0.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/5.0.1/HikariCP-5.0.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/6.1.1/spring-jdbc-6.1.1.jar:/Users/<USER>/.m2/repository/org/hibernate/orm/hibernate-core/6.3.1.Final/hibernate-core-6.3.1.Final.jar:/Users/<USER>/.m2/repository/jakarta/persistence/jakarta.persistence-api/3.1.0/jakarta.persistence-api-3.1.0.jar:/Users/<USER>/.m2/repository/jakarta/transaction/jakarta.transaction-api/2.0.1/jakarta.transaction-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.5.3.Final/jboss-logging-3.5.3.Final.jar:/Users/<USER>/.m2/repository/org/hibernate/common/hibernate-commons-annotations/6.0.6.Final/hibernate-commons-annotations-6.0.6.Final.jar:/Users/<USER>/.m2/repository/io/smallrye/jandex/3.1.2/jandex-3.1.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.6.0/classmate-1.6.0.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.14.10/byte-buddy-1.14.10.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/4.0.4/jaxb-runtime-4.0.4.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-core/4.0.4/jaxb-core-4.0.4.jar:/Users/<USER>/.m2/repository/org/eclipse/angus/angus-activation/2.0.1/angus-activation-2.0.1.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/txw2/4.0.4/txw2-4.0.4.jar:/Users/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/4.1.2/istack-commons-runtime-4.1.2.jar:/Users/<USER>/.m2/repository/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.10.1/antlr4-runtime-4.10.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jpa/3.2.0/spring-data-jpa-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.2.0/spring-data-commons-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/spring-orm/6.1.1/spring-orm-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.1.1/spring-tx-6.1.1.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.9/slf4j-api-2.0.9.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aspects/6.1.1/spring-aspects-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/3.2.0/spring-boot-starter-security-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.1.1/spring-aop-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/6.2.0/spring-security-config-6.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/6.2.0/spring-security-web-6.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.2.0/spring-boot-starter-validation-3.2.0.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.16/tomcat-embed-el-10.1.16.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.1.Final/hibernate-validator-8.0.1.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/3.2.0/spring-boot-starter-websocket-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/6.1.1/spring-messaging-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/6.1.1/spring-websocket-6.1.1.jar:/Users/<USER>/.m2/repository/org/postgresql/postgresql/42.6.0/postgresql-42.6.0.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.31.0/checker-qual-3.31.0.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.11.5/jjwt-api-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.11.5/jjwt-impl-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.11.5/jjwt-jackson-0.11.5.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.15.1/commons-io-2.15.1.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-webmvc-ui/2.3.0/springdoc-openapi-starter-webmvc-ui-2.3.0.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-webmvc-api/2.3.0/springdoc-openapi-starter-webmvc-api-2.3.0.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-common/2.3.0/springdoc-openapi-starter-common-2.3.0.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-core-jakarta/2.2.19/swagger-core-jakarta-2.2.19.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.13.0/commons-lang3-3.13.0.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations-jakarta/2.2.19/swagger-annotations-jakarta-2.2.19.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-models-jakarta/2.2.19/swagger-models-jakarta-2.2.19.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.15.3/jackson-dataformat-yaml-2.15.3.jar:/Users/<USER>/.m2/repository/org/webjars/swagger-ui/5.10.3/swagger-ui-5.10.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.15.3/jackson-databind-2.15.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.15.3/jackson-annotations-2.15.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.15.3/jackson-core-2.15.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.15.3/jackson-datatype-jsr310-2.15.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.2.0/spring-boot-starter-test-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.2.0/spring-boot-test-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.2.0/spring-boot-test-autoconfigure-3.2.0.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.8.0/json-path-2.8.0.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.1/jakarta.xml.bind-api-4.0.1.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.2/jakarta.activation-api-2.1.2.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.5.0/json-smart-2.5.0.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.0/accessors-smart-2.5.0.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/9.3/asm-9.3.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.24.2/assertj-core-3.24.2.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.0/awaitility-4.2.0.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.10.1/junit-jupiter-5.10.1.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.10.1/junit-jupiter-api-5.10.1.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.10.1/junit-platform-commons-1.10.1.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.10.1/junit-jupiter-params-5.10.1.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.10.1/junit-jupiter-engine-5.10.1.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.10.1/junit-platform-engine-1.10.1.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/5.7.0/mockito-core-5.7.0.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.14.10/byte-buddy-agent-1.14.10.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.7.0/mockito-junit-jupiter-5.7.0.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.1/jsonassert-1.5.1.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.1.1/spring-core-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.1.1/spring-jcl-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/6.1.1/spring-test-6.1.1.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.9.1/xmlunit-core-2.9.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-test/6.2.0/spring-security-test-6.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/6.2.0/spring-security-core-6.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.2.0/spring-security-crypto-6.2.0.jar:/Users/<USER>/.m2/repository/com/h2database/h2/2.2.224/h2-2.2.224.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.30/lombok-1.18.30.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/Users/<USER>"/>
    <property name="user.language" value="en"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2025-04-15"/>
    <property name="java.home" value="/opt/homebrew/Cellar/openjdk/24.0.1/libexec/openjdk.jdk/Contents/Home"/>
    <property name="file.separator" value="/"/>
    <property name="basedir" value="/Users/<USER>/workspaces/g/project-brief-buddy/backend"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="FILE_LOG_CHARSET" value="UTF-8"/>
    <property name="java.awt.headless" value="true"/>
    <property name="apple.awt.application.name" value="ForkedBooter"/>
    <property name="surefire.real.class.path" value="/Users/<USER>/workspaces/g/project-brief-buddy/backend/target/surefire/surefirebooter-20250714153952348_3.jar"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.runtime.version" value="24.0.1"/>
    <property name="user.name" value="koneti"/>
    <property name="stdout.encoding" value="UTF-8"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="15.5"/>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="java.vendor.version" value="Homebrew"/>
    <property name="localRepository" value="/Users/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues"/>
    <property name="java.io.tmpdir" value="/var/folders/8q/zd0c1xms73l7ljx5q0v13lt40000gn/T/"/>
    <property name="catalina.home" value="/private/var/folders/8q/zd0c1xms73l7ljx5q0v13lt40000gn/T/tomcat.0.4141616093792975888"/>
    <property name="com.zaxxer.hikari.pool_number" value="1"/>
    <property name="java.version" value="24.0.1"/>
    <property name="user.dir" value="/Users/<USER>/workspaces/g/project-brief-buddy/backend"/>
    <property name="os.arch" value="aarch64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="PID" value="49119"/>
    <property name="CONSOLE_LOG_CHARSET" value="UTF-8"/>
    <property name="catalina.base" value="/private/var/folders/8q/zd0c1xms73l7ljx5q0v13lt40000gn/T/tomcat.0.4141616093792975888"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="UTF-8"/>
    <property name="java.vendor" value="Homebrew"/>
    <property name="java.vm.version" value="24.0.1"/>
    <property name="sun.io.unicode.encoding" value="UnicodeBig"/>
    <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.class.version" value="68.0"/>
    <property name="LOGGED_APPLICATION_NAME" value="[project-brief-buddy-backend] "/>
  </properties>
  <testcase name="testUserRegistrationAndLogin" classname="com.projectbriefbuddy.integration.ProjectBriefBuddyIntegrationTest" time="0.572">
    <system-out><![CDATA[15:39:52.607 [main] INFO org.springframework.test.context.support.AnnotationConfigContextLoaderUtils -- Could not detect default configuration classes for test class [com.projectbriefbuddy.integration.ProjectBriefBuddyIntegrationTest]: ProjectBriefBuddyIntegrationTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration.
15:39:52.643 [main] INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper -- Found @SpringBootConfiguration com.projectbriefbuddy.ProjectBriefBuddyApplication for test class com.projectbriefbuddy.integration.ProjectBriefBuddyIntegrationTest

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
&amp#27;[32m :: Spring Boot :: &amp#27;[39m              &amp#27;[2m (v3.2.0)&amp#27;[0;39m

&amp#27;[2m2025-07-14T15:39:52.776+05:30&amp#27;[0;39m &amp#27;[32m INFO&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [           main]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mc.p.i.ProjectBriefBuddyIntegrationTest  &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Starting ProjectBriefBuddyIntegrationTest using Java 24.0.1 with PID 49119 (started by koneti in /Users/<USER>/workspaces/g/project-brief-buddy/backend)
&amp#27;[2m2025-07-14T15:39:52.776+05:30&amp#27;[0;39m &amp#27;[32mDEBUG&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [           main]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mc.p.i.ProjectBriefBuddyIntegrationTest  &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Running with Spring Boot v3.2.0, Spring v6.1.1
&amp#27;[2m2025-07-14T15:39:52.776+05:30&amp#27;[0;39m &amp#27;[32m INFO&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [           main]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mc.p.i.ProjectBriefBuddyIntegrationTest  &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m The following 1 profile is active: "test"
&amp#27;[2m2025-07-14T15:39:53.014+05:30&amp#27;[0;39m &amp#27;[32m INFO&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [           main]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36m.s.d.r.c.RepositoryConfigurationDelegate&amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Bootstrapping Spring Data JPA repositories in DEFAULT mode.
&amp#27;[2m2025-07-14T15:39:53.044+05:30&amp#27;[0;39m &amp#27;[32m INFO&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [           main]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36m.s.d.r.c.RepositoryConfigurationDelegate&amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Finished Spring Data repository scanning in 27 ms. Found 9 JPA repository interfaces.
&amp#27;[2m2025-07-14T15:39:53.265+05:30&amp#27;[0;39m &amp#27;[32m INFO&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [           main]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.s.b.w.embedded.tomcat.TomcatWebServer &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Tomcat initialized with port 0 (http)
&amp#27;[2m2025-07-14T15:39:53.268+05:30&amp#27;[0;39m &amp#27;[32m INFO&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [           main]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.apache.catalina.core.StandardService  &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Starting service [Tomcat]
&amp#27;[2m2025-07-14T15:39:53.268+05:30&amp#27;[0;39m &amp#27;[32m INFO&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [           main]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.apache.catalina.core.StandardEngine   &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Starting Servlet engine: [Apache Tomcat/10.1.16]
&amp#27;[2m2025-07-14T15:39:53.285+05:30&amp#27;[0;39m &amp#27;[32m INFO&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [           main]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.a.c.c.C.[Tomcat].[localhost].[/]      &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Initializing Spring embedded WebApplicationContext
&amp#27;[2m2025-07-14T15:39:53.285+05:30&amp#27;[0;39m &amp#27;[32m INFO&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [           main]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mw.s.c.ServletWebServerApplicationContext&amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Root WebApplicationContext: initialization completed in 504 ms
&amp#27;[2m2025-07-14T15:39:53.331+05:30&amp#27;[0;39m &amp#27;[32m INFO&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [           main]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.hibernate.jpa.internal.util.LogHelper &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m HHH000204: Processing PersistenceUnitInfo [name: default]
&amp#27;[2m2025-07-14T15:39:53.347+05:30&amp#27;[0;39m &amp#27;[32m INFO&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [           main]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36morg.hibernate.Version                   &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m HHH000412: Hibernate ORM core version 6.3.1.Final
&amp#27;[2m2025-07-14T15:39:53.357+05:30&amp#27;[0;39m &amp#27;[32m INFO&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [           main]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.h.c.internal.RegionFactoryInitiator   &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m HHH000026: Second-level cache disabled
&amp#27;[2m2025-07-14T15:39:53.427+05:30&amp#27;[0;39m &amp#27;[32m INFO&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [           main]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.s.o.j.p.SpringPersistenceUnitInfo     &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m No LoadTimeWeaver setup: ignoring JPA class transformer
&amp#27;[2m2025-07-14T15:39:53.434+05:30&amp#27;[0;39m &amp#27;[32m INFO&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [           main]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mcom.zaxxer.hikari.HikariDataSource      &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m HikariPool-1 - Starting...
&amp#27;[2m2025-07-14T15:39:53.494+05:30&amp#27;[0;39m &amp#27;[32m INFO&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [           main]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mcom.zaxxer.hikari.pool.HikariPool       &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m HikariPool-1 - Added connection conn0: url=jdbc:h2:mem:testdb user=SA
&amp#27;[2m2025-07-14T15:39:53.494+05:30&amp#27;[0;39m &amp#27;[32m INFO&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [           main]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mcom.zaxxer.hikari.HikariDataSource      &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m HikariPool-1 - Start completed.
&amp#27;[2m2025-07-14T15:39:53.503+05:30&amp#27;[0;39m &amp#27;[33m WARN&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [           main]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36morg.hibernate.orm.deprecation           &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
&amp#27;[2m2025-07-14T15:39:53.854+05:30&amp#27;[0;39m &amp#27;[32m INFO&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [           main]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.h.e.t.j.p.i.JtaPlatformInitiator      &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
&amp#27;[2m2025-07-14T15:39:53.880+05:30&amp#27;[0;39m &amp#27;[32m INFO&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [           main]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mj.LocalContainerEntityManagerFactoryBean&amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Initialized JPA EntityManagerFactory for persistence unit 'default'
&amp#27;[2m2025-07-14T15:39:54.013+05:30&amp#27;[0;39m &amp#27;[32mDEBUG&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [           main]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mc.p.security.JwtAuthenticationFilter    &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Filter 'jwtAuthenticationFilter' configured for use
&amp#27;[2m2025-07-14T15:39:54.034+05:30&amp#27;[0;39m &amp#27;[32m INFO&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [           main]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.s.d.j.r.query.QueryEnhancerFactory    &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Hibernate is in classpath; If applicable, HQL parser will be used.
&amp#27;[2m2025-07-14T15:39:54.353+05:30&amp#27;[0;39m &amp#27;[33m WARN&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [           main]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mJpaBaseConfiguration$JpaWebConfiguration&amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
&amp#27;[2m2025-07-14T15:39:54.458+05:30&amp#27;[0;39m &amp#27;[32m INFO&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [           main]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.s.s.web.DefaultSecurityFilterChain    &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@17e99817, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5fac8f7e, org.springframework.security.web.context.SecurityContextHolderFilter@56fdb4db, org.springframework.security.web.header.HeaderWriterFilter@515b9a68, org.springframework.web.filter.CorsFilter@29963f9a, org.springframework.security.web.authentication.logout.LogoutFilter@2250d39c, com.projectbriefbuddy.security.JwtAuthenticationFilter@75e068dc, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5839bf99, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4c53ea14, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3abc4498, org.springframework.security.web.session.SessionManagementFilter@302d0419, org.springframework.security.web.access.ExceptionTranslationFilter@47b8f783, org.springframework.security.web.access.intercept.AuthorizationFilter@3c9788c]
&amp#27;[2m2025-07-14T15:39:54.637+05:30&amp#27;[0;39m &amp#27;[32m INFO&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [           main]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.s.b.w.embedded.tomcat.TomcatWebServer &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Tomcat started on port 50828 (http) with context path ''
&amp#27;[2m2025-07-14T15:39:54.638+05:30&amp#27;[0;39m &amp#27;[32m INFO&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [           main]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.s.m.s.b.SimpleBrokerMessageHandler    &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Starting...
&amp#27;[2m2025-07-14T15:39:54.638+05:30&amp#27;[0;39m &amp#27;[32m INFO&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [           main]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.s.m.s.b.SimpleBrokerMessageHandler    &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@1d95957c]]
&amp#27;[2m2025-07-14T15:39:54.638+05:30&amp#27;[0;39m &amp#27;[32m INFO&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [           main]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.s.m.s.b.SimpleBrokerMessageHandler    &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Started.
&amp#27;[2m2025-07-14T15:39:54.642+05:30&amp#27;[0;39m &amp#27;[32m INFO&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [           main]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mc.p.i.ProjectBriefBuddyIntegrationTest  &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Started ProjectBriefBuddyIntegrationTest in 1.958 seconds (process running for 2.259)
&amp#27;[2m2025-07-14T15:39:54.948+05:30&amp#27;[0;39m &amp#27;[32m INFO&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [o-auto-1-exec-1]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.a.c.c.C.[Tomcat].[localhost].[/]      &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
&amp#27;[2m2025-07-14T15:39:54.948+05:30&amp#27;[0;39m &amp#27;[32m INFO&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [o-auto-1-exec-1]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.s.web.servlet.DispatcherServlet       &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Initializing Servlet 'dispatcherServlet'
&amp#27;[2m2025-07-14T15:39:54.948+05:30&amp#27;[0;39m &amp#27;[32m INFO&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [o-auto-1-exec-1]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.s.web.servlet.DispatcherServlet       &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Completed initialization in 0 ms
&amp#27;[2m2025-07-14T15:39:54.950+05:30&amp#27;[0;39m &amp#27;[32mDEBUG&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [o-auto-1-exec-1]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.s.security.web.FilterChainProxy       &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Securing POST /api/auth/register
&amp#27;[2m2025-07-14T15:39:54.953+05:30&amp#27;[0;39m &amp#27;[32mDEBUG&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [o-auto-1-exec-1]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.s.s.w.a.AnonymousAuthenticationFilter &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Set SecurityContextHolder to anonymous SecurityContext
&amp#27;[2m2025-07-14T15:39:54.953+05:30&amp#27;[0;39m &amp#27;[33m WARN&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [o-auto-1-exec-1]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.s.w.s.h.HandlerMappingIntrospector    &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Cache miss for REQUEST dispatch to '/api/auth/register' (previous null). Performing MatchableHandlerMapping lookup. This is logged once only at WARN level, and every time at TRACE.
&amp#27;[2m2025-07-14T15:39:54.955+05:30&amp#27;[0;39m &amp#27;[32mDEBUG&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [o-auto-1-exec-1]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.s.security.web.FilterChainProxy       &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Secured POST /api/auth/register
&amp#27;[2m2025-07-14T15:39:55.095+05:30&amp#27;[0;39m &amp#27;[32mDEBUG&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [o-auto-1-exec-2]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.s.security.web.FilterChainProxy       &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Securing POST /api/auth/login
&amp#27;[2m2025-07-14T15:39:55.095+05:30&amp#27;[0;39m &amp#27;[32mDEBUG&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [o-auto-1-exec-2]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.s.s.w.a.AnonymousAuthenticationFilter &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Set SecurityContextHolder to anonymous SecurityContext
&amp#27;[2m2025-07-14T15:39:55.096+05:30&amp#27;[0;39m &amp#27;[32mDEBUG&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [o-auto-1-exec-2]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.s.security.web.FilterChainProxy       &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Secured POST /api/auth/login
&amp#27;[2m2025-07-14T15:39:55.206+05:30&amp#27;[0;39m &amp#27;[32mDEBUG&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [o-auto-1-exec-2]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.s.s.a.dao.DaoAuthenticationProvider   &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Authenticated user
]]></system-out>
    <system-err><![CDATA[WARNING: A Java agent has been loaded dynamically (/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.14.10/byte-buddy-agent-1.14.10.jar)
WARNING: If a serviceability tool is in use, please run with -XX:+EnableDynamicAgentLoading to hide this warning
WARNING: If a serviceability tool is not in use, please run with -Djdk.instrument.traceUsage for more information
WARNING: Dynamic loading of agents will be disallowed by default in a future release
]]></system-err>
  </testcase>
  <testcase name="testDatabaseConnectivity" classname="com.projectbriefbuddy.integration.ProjectBriefBuddyIntegrationTest" time="0.083">
    <error message="Row was updated or deleted by another transaction (or unsaved-value mapping was incorrect) : [com.projectbriefbuddy.entity.RefreshToken#a315fa9f-5a45-4a45-8d71-5c6c8cc22b1a]" type="org.springframework.orm.ObjectOptimisticLockingFailureException"><![CDATA[org.springframework.orm.ObjectOptimisticLockingFailureException: Row was updated or deleted by another transaction (or unsaved-value mapping was incorrect) : [com.projectbriefbuddy.entity.RefreshToken#a315fa9f-5a45-4a45-8d71-5c6c8cc22b1a]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:325)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:244)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:550)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:335)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:152)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:164)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:249)
	at jdk.proxy2/jdk.proxy2.$Proxy155.count(Unknown Source)
	at com.projectbriefbuddy.integration.ProjectBriefBuddyIntegrationTest.testDatabaseConnectivity(ProjectBriefBuddyIntegrationTest.java:126)
	at java.base/java.lang.reflect.Method.invoke(Method.java:565)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1604)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1604)
Caused by: org.hibernate.StaleObjectStateException: Row was updated or deleted by another transaction (or unsaved-value mapping was incorrect) : [com.projectbriefbuddy.entity.RefreshToken#a315fa9f-5a45-4a45-8d71-5c6c8cc22b1a]
	at org.hibernate.engine.jdbc.mutation.internal.ModelMutationHelper.identifiedResultsCheck(ModelMutationHelper.java:75)
	at org.hibernate.persister.entity.mutation.DeleteCoordinator.lambda$doStaticDelete$2(DeleteCoordinator.java:271)
	at org.hibernate.engine.jdbc.mutation.internal.ModelMutationHelper.checkResults(ModelMutationHelper.java:50)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.performNonBatchedMutation(AbstractMutationExecutor.java:114)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorSingleNonBatched.performNonBatchedOperations(MutationExecutorSingleNonBatched.java:40)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.execute(AbstractMutationExecutor.java:52)
	at org.hibernate.persister.entity.mutation.DeleteCoordinator.doStaticDelete(DeleteCoordinator.java:267)
	at org.hibernate.persister.entity.mutation.DeleteCoordinator.coordinateDelete(DeleteCoordinator.java:86)
	at org.hibernate.persister.entity.AbstractEntityPersister.delete(AbstractEntityPersister.java:2971)
	at org.hibernate.action.internal.EntityDeleteAction.execute(EntityDeleteAction.java:131)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:631)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:498)
	at org.hibernate.event.internal.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:363)
	at org.hibernate.event.internal.DefaultAutoFlushEventListener.onAutoFlush(DefaultAutoFlushEventListener.java:55)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.autoFlushIfRequired(SessionImpl.java:1378)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.lambda$new$0(ConcreteSqmSelectQueryPlan.java:107)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.withCacheableSqmInterpretation(ConcreteSqmSelectQueryPlan.java:305)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.performList(ConcreteSqmSelectQueryPlan.java:246)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doList(QuerySqmImpl.java:509)
	at org.hibernate.query.spi.AbstractSelectionQuery.list(AbstractSelectionQuery.java:427)
	at org.hibernate.query.spi.AbstractSelectionQuery.getSingleResult(AbstractSelectionQuery.java:559)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.count(SimpleJpaRepository.java:603)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:565)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:516)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:285)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:628)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:168)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:143)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	... 11 more
]]></error>
    <system-out><![CDATA[&amp#27;[2m2025-07-14T15:39:55.226+05:30&amp#27;[0;39m &amp#27;[32mDEBUG&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [o-auto-1-exec-3]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.s.security.web.FilterChainProxy       &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Securing POST /api/auth/register
&amp#27;[2m2025-07-14T15:39:55.227+05:30&amp#27;[0;39m &amp#27;[32mDEBUG&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [o-auto-1-exec-3]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.s.s.w.a.AnonymousAuthenticationFilter &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Set SecurityContextHolder to anonymous SecurityContext
&amp#27;[2m2025-07-14T15:39:55.227+05:30&amp#27;[0;39m &amp#27;[32mDEBUG&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [o-auto-1-exec-3]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.s.security.web.FilterChainProxy       &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Secured POST /api/auth/register
&amp#27;[2m2025-07-14T15:39:55.229+05:30&amp#27;[0;39m &amp#27;[31mERROR&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [o-auto-1-exec-3]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mc.p.exception.GlobalExceptionHandler    &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Bad request exception: Email already registered
&amp#27;[2m2025-07-14T15:39:55.232+05:30&amp#27;[0;39m &amp#27;[32mDEBUG&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [o-auto-1-exec-4]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.s.security.web.FilterChainProxy       &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Securing POST /api/auth/login
&amp#27;[2m2025-07-14T15:39:55.232+05:30&amp#27;[0;39m &amp#27;[32mDEBUG&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [o-auto-1-exec-4]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.s.s.w.a.AnonymousAuthenticationFilter &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Set SecurityContextHolder to anonymous SecurityContext
&amp#27;[2m2025-07-14T15:39:55.232+05:30&amp#27;[0;39m &amp#27;[32mDEBUG&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [o-auto-1-exec-4]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.s.security.web.FilterChainProxy       &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Secured POST /api/auth/login
&amp#27;[2m2025-07-14T15:39:55.286+05:30&amp#27;[0;39m &amp#27;[32mDEBUG&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [o-auto-1-exec-4]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.s.s.a.dao.DaoAuthenticationProvider   &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Authenticated user
]]></system-out>
  </testcase>
  <testcase name="testProjectCreationAndRetrieval" classname="com.projectbriefbuddy.integration.ProjectBriefBuddyIntegrationTest" time="0.083">
    <failure type="java.lang.AssertionError"><![CDATA[java.lang.AssertionError
	at com.projectbriefbuddy.integration.ProjectBriefBuddyIntegrationTest.testProjectCreationAndRetrieval(ProjectBriefBuddyIntegrationTest.java:107)
	at java.base/java.lang.reflect.Method.invoke(Method.java:565)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1604)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1604)
]]></failure>
    <system-out><![CDATA[&amp#27;[2m2025-07-14T15:39:55.307+05:30&amp#27;[0;39m &amp#27;[32mDEBUG&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [o-auto-1-exec-5]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.s.security.web.FilterChainProxy       &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Securing POST /api/auth/register
&amp#27;[2m2025-07-14T15:39:55.308+05:30&amp#27;[0;39m &amp#27;[32mDEBUG&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [o-auto-1-exec-5]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.s.s.w.a.AnonymousAuthenticationFilter &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Set SecurityContextHolder to anonymous SecurityContext
&amp#27;[2m2025-07-14T15:39:55.308+05:30&amp#27;[0;39m &amp#27;[32mDEBUG&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [o-auto-1-exec-5]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.s.security.web.FilterChainProxy       &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Secured POST /api/auth/register
&amp#27;[2m2025-07-14T15:39:55.309+05:30&amp#27;[0;39m &amp#27;[31mERROR&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [o-auto-1-exec-5]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mc.p.exception.GlobalExceptionHandler    &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Bad request exception: Email already registered
&amp#27;[2m2025-07-14T15:39:55.311+05:30&amp#27;[0;39m &amp#27;[32mDEBUG&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [o-auto-1-exec-6]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.s.security.web.FilterChainProxy       &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Securing POST /api/auth/login
&amp#27;[2m2025-07-14T15:39:55.311+05:30&amp#27;[0;39m &amp#27;[32mDEBUG&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [o-auto-1-exec-6]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.s.s.w.a.AnonymousAuthenticationFilter &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Set SecurityContextHolder to anonymous SecurityContext
&amp#27;[2m2025-07-14T15:39:55.311+05:30&amp#27;[0;39m &amp#27;[32mDEBUG&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [o-auto-1-exec-6]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.s.security.web.FilterChainProxy       &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Secured POST /api/auth/login
&amp#27;[2m2025-07-14T15:39:55.365+05:30&amp#27;[0;39m &amp#27;[32mDEBUG&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [o-auto-1-exec-6]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.s.s.a.dao.DaoAuthenticationProvider   &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Authenticated user
&amp#27;[2m2025-07-14T15:39:55.371+05:30&amp#27;[0;39m &amp#27;[32mDEBUG&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [o-auto-1-exec-7]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.s.security.web.FilterChainProxy       &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Securing POST /api/projects
&amp#27;[2m2025-07-14T15:39:55.378+05:30&amp#27;[0;39m &amp#27;[32mDEBUG&amp#27;[0;39m &amp#27;[35m49119&amp#27;[0;39m &amp#27;[2m---&amp#27;[0;39m &amp#27;[2m[project-brief-buddy-backend] [o-auto-1-exec-7]&amp#27;[0;39m &amp#27;[2m&amp#27;[0;39m&amp#27;[36mo.s.security.web.FilterChainProxy       &amp#27;[0;39m &amp#27;[2m:&amp#27;[0;39m Secured POST /api/projects
]]></system-out>
  </testcase>
</testsuite>