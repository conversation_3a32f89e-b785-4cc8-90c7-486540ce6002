package com.projectbriefbuddy.service.ai;

import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.input.Prompt;
import dev.langchain4j.model.input.PromptTemplate;
import dev.langchain4j.model.output.Response;
import dev.langchain4j.data.document.Document;
import dev.langchain4j.data.document.DocumentSplitter;
import dev.langchain4j.data.document.splitter.DocumentSplitters;
import dev.langchain4j.data.segment.TextSegment;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class RequirementsExtractionService {

    private final ChatLanguageModel chatLanguageModel;

    private static final String REQUIREMENTS_EXTRACTION_PROMPT = """
        You are an expert business analyst. Extract structured project requirements from the following text.
        
        Text: {{text}}
        
        Please extract and organize the following information in JSON format:
        
        {
          "projectOverview": {
            "name": "extracted project name",
            "description": "brief description",
            "businessObjectives": ["objective 1", "objective 2"],
            "stakeholders": ["stakeholder 1", "stakeholder 2"]
          },
          "functionalRequirements": [
            {
              "category": "category name",
              "requirements": [
                {
                  "id": "unique identifier",
                  "title": "requirement title",
                  "description": "detailed description",
                  "priority": "HIGH|MEDIUM|LOW",
                  "acceptance_criteria": ["criteria 1", "criteria 2"]
                }
              ]
            }
          ],
          "nonFunctionalRequirements": [
            {
              "category": "Performance|Security|Scalability|Usability|Reliability",
              "requirements": [
                {
                  "title": "requirement title",
                  "description": "detailed description",
                  "metrics": "measurable criteria"
                }
              ]
            }
          ],
          "technicalRequirements": {
            "platforms": ["platform 1", "platform 2"],
            "technologies": ["tech 1", "tech 2"],
            "integrations": ["system 1", "system 2"],
            "databases": ["database type"],
            "apis": ["api 1", "api 2"]
          },
          "userStories": [
            {
              "role": "user role",
              "goal": "what they want to achieve",
              "benefit": "why they want it",
              "acceptanceCriteria": ["criteria 1", "criteria 2"]
            }
          ],
          "constraints": {
            "budget": "budget information if mentioned",
            "timeline": "timeline information if mentioned",
            "resources": "resource constraints if mentioned",
            "regulatory": "compliance requirements if mentioned"
          },
          "assumptions": ["assumption 1", "assumption 2"],
          "risks": [
            {
              "description": "risk description",
              "impact": "HIGH|MEDIUM|LOW",
              "mitigation": "mitigation strategy"
            }
          ]
        }
        
        If any section has no relevant information, return an empty array or null values.
        Respond only with valid JSON.
        """;

    private static final String REQUIREMENTS_REFINEMENT_PROMPT = """
        You are an expert business analyst. Review and refine the following project requirements.
        
        Current Requirements: {{requirements}}
        
        Please:
        1. Identify any missing critical requirements
        2. Suggest improvements to existing requirements
        3. Flag any inconsistencies or conflicts
        4. Recommend additional user stories if needed
        5. Validate that acceptance criteria are clear and testable
        
        Provide your response in JSON format:
        {
          "suggestions": [
            {
              "type": "MISSING|IMPROVEMENT|CONFLICT|ADDITION",
              "category": "category name",
              "description": "detailed suggestion",
              "priority": "HIGH|MEDIUM|LOW"
            }
          ],
          "refinedRequirements": {
            // Include the refined version of the requirements with improvements
          }
        }
        """;

    public String extractRequirementsFromText(String text) {
        log.info("Extracting requirements from text of length: {}", text.length());
        
        try {
            PromptTemplate template = PromptTemplate.from(REQUIREMENTS_EXTRACTION_PROMPT);
            Prompt prompt = template.apply(Map.of("text", text));
            
            Response<String> response = chatLanguageModel.generate(prompt.toUserMessage());
            
            String extractedRequirements = response.content();
            log.info("Successfully extracted requirements");
            
            return extractedRequirements;
        } catch (Exception e) {
            log.error("Error extracting requirements from text", e);
            throw new RuntimeException("Failed to extract requirements: " + e.getMessage());
        }
    }

    public String refineRequirements(String currentRequirements) {
        log.info("Refining requirements");
        
        try {
            PromptTemplate template = PromptTemplate.from(REQUIREMENTS_REFINEMENT_PROMPT);
            Prompt prompt = template.apply(Map.of("requirements", currentRequirements));
            
            Response<String> response = chatLanguageModel.generate(prompt.toUserMessage());
            
            String refinedRequirements = response.content();
            log.info("Successfully refined requirements");
            
            return refinedRequirements;
        } catch (Exception e) {
            log.error("Error refining requirements", e);
            throw new RuntimeException("Failed to refine requirements: " + e.getMessage());
        }
    }

    public List<TextSegment> splitTextIntoSegments(String text) {
        Document document = Document.from(text);
        DocumentSplitter splitter = DocumentSplitters.recursive(1000, 200);
        return splitter.split(document);
    }

    public String generateQuestionsForMissingInfo(String partialRequirements) {
        String questionsPrompt = """
            Based on the following partial requirements, generate specific questions that would help gather missing information:
            
            Requirements: {{requirements}}
            
            Generate 5-10 specific questions that would help clarify:
            - Business objectives and success criteria
            - User roles and personas
            - Functional requirements gaps
            - Technical constraints
            - Integration requirements
            - Performance expectations
            
            Format as a JSON array of strings:
            ["Question 1?", "Question 2?", ...]
            """;
        
        try {
            PromptTemplate template = PromptTemplate.from(questionsPrompt);
            Prompt prompt = template.apply(Map.of("requirements", partialRequirements));
            
            Response<String> response = chatLanguageModel.generate(prompt.toUserMessage());
            return response.content();
        } catch (Exception e) {
            log.error("Error generating questions for missing info", e);
            throw new RuntimeException("Failed to generate questions: " + e.getMessage());
        }
    }
}