# Project Brief Buddy Backend

A Spring Boot backend application for Project Brief Buddy - A tool for capturing and managing project requirements through audio recordings and brief generation.

## Features

- **JWT Authentication**: Secure user authentication with access and refresh tokens
- **Project Management**: Create, read, update, and delete projects
- **Audio Recording Management**: Upload, store, and manage audio recordings
- **Brief Generation**: Create and manage project briefs with sections
- **File Storage**: Local file storage for audio recordings
- **Real-time Updates**: WebSocket support for real-time notifications
- **API Documentation**: Swagger/OpenAPI documentation
- **Validation**: Request validation and error handling

## Technology Stack

- **Java 17**
- **Spring Boot 3.2.0**
- **Spring Security** (JWT authentication)
- **Spring Data JPA** (PostgreSQL)
- **Spring WebSocket** (Real-time features)
- **Lombok** (Boilerplate code reduction)
- **Maven** (Build tool)
- **Swagger/OpenAPI** (API documentation)

## Prerequisites

- Java 17 or higher
- PostgreSQL 15+
- Maven 3.6+

## Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd project-brief-buddy/backend
   ```

2. **Configure PostgreSQL**
   - Create a database named `project_brief_buddy`
   - Update database credentials in `application.yml`

3. **Set environment variables** (optional)
   ```bash
   export DB_USERNAME=your_db_username
   export DB_PASSWORD=your_db_password
   export JWT_SECRET=your_jwt_secret
   ```

4. **Run the application**
   ```bash
   mvn spring-boot:run
   ```

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login user
- `POST /api/auth/refresh` - Refresh access token
- `POST /api/auth/logout` - Logout user

### Projects
- `GET /api/projects` - Get all projects (paginated)
- `POST /api/projects` - Create new project
- `GET /api/projects/{id}` - Get project by ID
- `PUT /api/projects/{id}` - Update project
- `DELETE /api/projects/{id}` - Delete project

### Recordings
- `GET /api/projects/{id}/recordings` - Get project recordings
- `POST /api/projects/{id}/recordings` - Upload new recording
- `GET /api/projects/{id}/recordings/{recordingId}` - Get recording details
- `PUT /api/projects/{id}/recordings/{recordingId}` - Update recording
- `DELETE /api/projects/{id}/recordings/{recordingId}` - Delete recording
- `GET /api/projects/{id}/recordings/{recordingId}/download` - Download recording

### Briefs
- `GET /api/projects/{id}/briefs` - Get project briefs
- `POST /api/projects/{id}/briefs` - Create new brief
- `GET /api/projects/{id}/briefs/{briefId}` - Get brief details
- `PUT /api/projects/{id}/briefs/{briefId}` - Update brief
- `POST /api/projects/{id}/briefs/{briefId}/finalize` - Finalize brief
- `DELETE /api/projects/{id}/briefs/{briefId}` - Delete brief

## Database Schema

The application uses PostgreSQL with the following main tables:
- `users` - User accounts
- `projects` - Project information
- `recordings` - Audio recording metadata
- `briefs` - Project briefs
- `brief_sections` - Brief sections
- `project_collaborators` - Project collaboration
- `refresh_tokens` - JWT refresh tokens

## Configuration

Key configuration options in `application.yml`:

```yaml
spring:
  datasource:
    url: ****************************************************
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:postgres}
  
  security:
    jwt:
      secret: ${JWT_SECRET:your-secret-key}
      expiration: ******** # 24 hours
      refresh-expiration: ********* # 7 days

file:
  upload-dir: ${FILE_UPLOAD_DIR:./uploads}
```

## API Documentation

Once the application is running, you can access:
- Swagger UI: http://localhost:8080/swagger-ui.html
- API Docs: http://localhost:8080/api-docs

## WebSocket Support

The application supports WebSocket connections for real-time updates:
- Endpoint: `/ws`
- Topics:
  - `/topic/project/{projectId}` - Project updates
  - `/topic/project/{projectId}/recordings` - Recording updates
  - `/topic/project/{projectId}/briefs` - Brief updates
  - `/user/queue/notifications` - User notifications

## Security

- JWT-based authentication
- CORS configuration for frontend integration
- Input validation and sanitization
- Secure file upload with size limits
- SQL injection prevention through JPA

## Development

### Running Tests
```bash
mvn test
```

### Building for Production
```bash
mvn clean package -DskipTests
```

### Docker Support
```bash
docker build -t project-brief-buddy-backend .
docker run -p 8080:8080 project-brief-buddy-backend
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.